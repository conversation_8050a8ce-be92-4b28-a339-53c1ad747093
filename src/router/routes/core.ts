import { $t } from '@/locales'
import { NotFound } from '@/views/errors'
import { ForgotPassword, LoginForm } from '@/views/login'
import type { RouteRecordRaw } from 'vue-router'
import { LOGIN_PATH } from '@/core'

/** 全局404页面 */
const fallbackNotFoundRoute: RouteRecordRaw = {
  component: NotFound,
  meta: {
    hideInBreadcrumb: true,
    hideInMenu: true,
    hideInTab: true,
    title: '404',
  },
  name: 'FallbackNotFound',
  path: '/:path(.*)*',
}

/** 基本路由，这些路由是必须存在的 */
const coreRoutes: RouteRecordRaw[] = [
  /**
   * 根路由
   * 使用基础布局，作为所有页面的父级容器，子级就不必配置BasicLayout。
   * 此路由必须存在，且不应修改
   */
  {
    path: '/',
    name: 'Root',
    component: () => import('@/layouts/layout.vue'),
    meta: {
      hideInBreadcrumb: true,
      title: '系统管理',
    },
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/datacenter/dashboard.vue'),
        meta: {
          title: $t('menu.dashboard'),
          requiresAuth: true,
        },
      },
      // {
      //     path: '/test-status-renderer',
      //     name: 'TestStatusRenderer',
      //     component: () => import('@/components/StatusRendererTest.vue'),
      //     meta: {
      //         title: 'StatusRenderer 测试',
      //         requiresAuth: false,
      //     },
      // },
      // {
      //     path: '/test-workflow',
      //     name: 'TestWorkflow',
      //     component: () => import('@/views/bas/test-workflow/index.vue'),
      //     meta: {
      //         title: '测试工作流',
      //         requiresAuth: true,
      //     },
      // },
      {
        path: '/sys/user/profile',
        name: 'profile',
        component: () => import('@/views/sys/user/profile.vue'),
        meta: {
          title: $t('menu.sys.user.profile'),
          requiresAuth: true,
        },
      },
      {
        path: '/sys/user/settings',
        name: 'settings',
        component: () => import('@/views/sys/user/settings.vue'),
        meta: {
          title: $t('menu.sys.user.settings'),
          requiresAuth: true,
        },
      },
      {
        path: '/workflow/config',
        name: 'workflowConfig',
        component: () => import('@/views/workflow/config.vue'),
        meta: {
          title: $t('menu.workflow.config'),
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/pad-measurement',
    name: 'padMeasurement',
    component: () => import('@/views/pm/pad-measurement/index.vue'),
    meta: {
      hideInTab: true,
      title: '复尺管理',
    },
  },
  {
    path: '/auth',
    name: 'Authentication',
    component: () => import('@/layouts/login.vue'),
    meta: {
      hideInTab: true,
      title: 'Authentication',
    },
    redirect: LOGIN_PATH,
    children: [
      {
        name: 'Login',
        path: 'login',
        component: LoginForm,
        meta: {
          title: $t('sys.auth.login.title'),
        },
      },
      {
        name: 'ForgotPassword',
        path: 'forgot-password',
        component: ForgotPassword,
        meta: {
          title: $t('sys.auth.login.forgotPassword'),
        },
      },
    ],
  },
]

export { coreRoutes, fallbackNotFoundRoute }
