<template>
  <Transition
    name="loading-fade"
    enter-active-class="transition-opacity duration-300 ease-out"
    leave-active-class="transition-opacity duration-200 ease-in"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="visible"
      class="absolute inset-0 z-50 flex items-center justify-center"
      :class="overlayClasses"
      :style="overlayStyles"
      role="status"
      :aria-label="ariaLabel"
      aria-live="polite"
    >
      <div
        class="flex flex-col items-center justify-center space-y-3 p-6 rounded-lg"
        :class="containerClasses"
        :style="containerStyles"
      >
        <!-- 动画图标区域 -->
        <div
          class="flex items-center justify-center"
          :style="iconContainerStyles"
        >
          <!-- Spinner 动画 -->
          <div
            v-if="config.type === 'spinner'"
            class="animate-spin"
            :style="spinnerStyles"
          >
            <svg
              class="w-full h-full"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-dasharray="31.416"
                stroke-dashoffset="31.416"
                class="opacity-25"
              />
              <path
                d="M12 2a10 10 0 0 1 10 10"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                class="opacity-75"
              />
            </svg>
          </div>

          <!-- Dots 动画 -->
          <div
            v-else-if="config.type === 'dots'"
            class="flex space-x-1"
            :style="dotsContainerStyles"
          >
            <div
              v-for="i in 3"
              :key="i"
              class="rounded-full animate-pulse"
              :class="`animate-bounce-${i}`"
              :style="dotStyles"
            ></div>
          </div>

          <!-- Bars 动画 -->
          <div
            v-else-if="config.type === 'bars'"
            class="flex items-end space-x-1"
            :style="barsContainerStyles"
          >
            <div
              v-for="i in 4"
              :key="i"
              class="bg-current animate-pulse"
              :class="`animate-bar-${i}`"
              :style="barStyles(i)"
            ></div>
          </div>

          <!-- Pulse 动画 -->
          <div
            v-else-if="config.type === 'pulse'"
            class="relative"
            :style="pulseContainerStyles"
          >
            <div
              class="absolute inset-0 rounded-full bg-current animate-ping opacity-20"
            ></div>
            <div
              class="relative rounded-full bg-current animate-pulse"
              :style="pulseStyles"
            ></div>
          </div>
        </div>

        <!-- 加载文本 -->
        <div
          v-if="config.text"
          class="text-center font-medium"
          :class="textClasses"
          :style="textStyles"
        >
          {{ config.text }}
        </div>

        <!-- 自定义插槽 -->
        <slot name="custom" :config="config" />
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, type CSSProperties } from 'vue'
import type { DataGridLoadingConfig } from '../types'

interface Props {
  visible: boolean
  config: DataGridLoadingConfig
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  config: () => ({
    type: 'spinner',
    text: '数据加载中...',
    size: 'medium',
    color: 'primary',
    theme: 'auto',
    overlay: {
      opacity: 0.8,
      blur: true,
    },
    animation: {
      speed: 'normal',
    },
  }),
})

// 计算 aria-label
const ariaLabel = computed(() => {
  return props.config.text || '数据加载中，请稍候'
})

// 主题相关的计算属性
const isDark = computed(() => {
  if (props.config.theme === 'dark') return true
  if (props.config.theme === 'light') return false
  // auto 模式下检测系统主题
  return window.matchMedia('(prefers-color-scheme: dark)').matches
})

// 遮罩层样式
const overlayClasses = computed(() => [
  'backdrop-blur-sm',
  isDark.value ? 'bg-gray-900/80' : 'bg-white/80',
])

const overlayStyles = computed(
  (): CSSProperties => ({
    backgroundColor: props.config.overlay?.backgroundColor || undefined,
    backdropFilter: props.config.overlay?.blur ? 'blur(2px)' : 'none',
    opacity: props.config.overlay?.opacity || 0.8,
  })
)

// 容器样式
const containerClasses = computed(() => [
  'shadow-lg border',
  isDark.value
    ? 'bg-gray-800 border-gray-700 text-white'
    : 'bg-white border-gray-200 text-gray-900',
])

const containerStyles = computed(
  (): CSSProperties => ({
    backgroundColor: props.config.containerBackground || undefined,
    borderRadius: '12px',
    minWidth: '120px',
  })
)

// 尺寸映射
const sizeMap = {
  small: { icon: 24, text: 'text-sm' },
  medium: { icon: 32, text: 'text-base' },
  large: { icon: 40, text: 'text-lg' },
}

const currentSize = computed(() => sizeMap[props.config.size || 'medium'])

// 颜色映射
const colorMap = {
  primary: isDark.value ? '#3b82f6' : '#2563eb',
  success: isDark.value ? '#10b981' : '#059669',
  warning: isDark.value ? '#f59e0b' : '#d97706',
  error: isDark.value ? '#ef4444' : '#dc2626',
  info: isDark.value ? '#06b6d4' : '#0891b2',
}

const currentColor = computed(() => {
  return props.config.color && typeof props.config.color === 'string'
    ? colorMap[props.config.color as keyof typeof colorMap] ||
        props.config.color
    : colorMap.primary
})

// 动画速度映射
const speedMap = {
  slow: '2s',
  normal: '1.5s',
  fast: '1s',
}

const animationDuration = computed(() => {
  return speedMap[props.config.animation?.speed || 'normal']
})

// 图标容器样式
const iconContainerStyles = computed(
  (): CSSProperties => ({
    width: `${currentSize.value.icon}px`,
    height: `${currentSize.value.icon}px`,
    color: currentColor.value,
  })
)

// Spinner 样式
const spinnerStyles = computed(
  (): CSSProperties => ({
    animationDuration: animationDuration.value,
  })
)

// Dots 相关样式
const dotsContainerStyles = computed(
  (): CSSProperties => ({
    gap: '4px',
  })
)

const dotStyles = computed(
  (): CSSProperties => ({
    width: `${Math.max(6, currentSize.value.icon / 4)}px`,
    height: `${Math.max(6, currentSize.value.icon / 4)}px`,
    backgroundColor: currentColor.value,
    animationDuration: animationDuration.value,
  })
)

// Bars 相关样式
const barsContainerStyles = computed(
  (): CSSProperties => ({
    height: `${currentSize.value.icon}px`,
    gap: '2px',
  })
)

const barStyles = (index: number): CSSProperties => ({
  width: `${Math.max(3, currentSize.value.icon / 8)}px`,
  height: `${20 + index * 10}%`,
  backgroundColor: currentColor.value,
  animationDuration: animationDuration.value,
  animationDelay: `${index * 0.1}s`,
})

// Pulse 相关样式
const pulseContainerStyles = computed(
  (): CSSProperties => ({
    width: `${currentSize.value.icon}px`,
    height: `${currentSize.value.icon}px`,
  })
)

const pulseStyles = computed(
  (): CSSProperties => ({
    width: '60%',
    height: '60%',
    backgroundColor: currentColor.value,
    animationDuration: animationDuration.value,
  })
)

// 文本样式
const textClasses = computed(() => [
  currentSize.value.text,
  isDark.value ? 'text-gray-200' : 'text-gray-700',
])

const textStyles = computed(
  (): CSSProperties => ({
    color: props.config.textColor || undefined,
    maxWidth: '200px',
    lineHeight: '1.4',
  })
)
</script>

<style scoped>
/* 自定义动画 */
@keyframes bounce-1 {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes bounce-2 {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes bounce-3 {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.animate-bounce-1 {
  animation: bounce-1 1.4s infinite ease-in-out;
}
.animate-bounce-2 {
  animation: bounce-2 1.4s infinite ease-in-out 0.16s;
}
.animate-bounce-3 {
  animation: bounce-3 1.4s infinite ease-in-out 0.32s;
}

@keyframes bar-1 {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes bar-2 {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes bar-3 {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes bar-4 {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

.animate-bar-1 {
  animation: bar-1 1.2s infinite ease-in-out;
}
.animate-bar-2 {
  animation: bar-2 1.2s infinite ease-in-out 0.1s;
}
.animate-bar-3 {
  animation: bar-3 1.2s infinite ease-in-out 0.2s;
}
.animate-bar-4 {
  animation: bar-4 1.2s infinite ease-in-out 0.3s;
}

/* 确保动画性能优化 */
.animate-spin,
.animate-pulse,
.animate-ping,
[class*='animate-bounce-'],
[class*='animate-bar-'] {
  will-change: transform;
}
</style>
