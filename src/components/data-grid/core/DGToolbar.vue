<template>
  <div class="toolbar border-b border-gray-200 px-2 py-1 space-y-2">
    <!-- 响应式容器 -->
    <div
      class="grid gap-1 xl:grid-cols-[auto_1fr_auto] xl:grid-rows-1 xl:items-center md:grid-cols-[auto_auto] md:grid-rows-2 md:items-center grid-cols-1 grid-rows-3"
    >
      <!-- 第一部分：标题和操作按钮组 -->
      <div
        class="flex items-center gap-2 xl:col-start-1 xl:row-start-1 md:col-start-1 md:row-start-1 col-start-1 row-start-1"
      >
        <DGToolbarTitle
          v-if="titleProps"
          v-bind="titleProps"
          ref="dgToolbarTitleRef"
        />
        <DGToolbarButton v-if="actionProps" v-bind="actionProps" />
      </div>

      <!-- 第二部分：分页和表格操作组 -->
      <div
        class="flex flex-none items-center gap-2 justify-end xl:col-start-3 xl:row-start-1 md:col-start-2 md:row-start-1 col-start-1 row-start-2"
      >
        <div class="flex items-center">
          <DGToolbarRange v-bind="rangeProps" @dataFetch="handleDataFetch" />
        </div>
        <div class="flex items-center">
          <DGToolbarAction
            v-if="toolbarActionProps"
            v-bind="toolbarActionProps"
          />
        </div>
      </div>

      <!-- 第三部分：搜索区 -->
      <div
        class="flex justify-center xl:col-start-2 xl:row-start-1 xl:max-w-[calc(100%-18rem)] md:col-span-full md:row-start-2 col-start-1 row-start-3"
      >
        <DGToolbarSearch class="mx-auto" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, inject, provide, ref, watch } from 'vue'
import DGToolbarTitle from './DGToolbarTitle.vue'
import DGToolbarButton from './DGToolbarButton.vue'
import DGToolbarAction from './DGToolbarAction.vue'
import DGToolbarRange from './DGToolbarRange.vue'
import { DGToolbarSearch } from './DGToolbarSearch'
import type { ToolbarProps, ToolbarTitleProps } from '../types'
import { QueryParams } from '@/types/api/queryParams'

const props = withDefaults(defineProps<ToolbarProps>(), {})

const dgToolbarTitleRef = ref<InstanceType<typeof DGToolbarTitle> | null>(null)

// 处理标题属性
const titleProps = computed((): ToolbarTitleProps | null => {
  if (!props.title) return null

  if (typeof props.title === 'string') {
    return {
      main: props.title,
    }
  }

  return props.title
})

// 注入 DataGrid 实例 (如果有的话)
const dataGridInstance = inject('dataGridInstance', null)
// 提供 dataGridInstance 给子组件使用
provide('dataGridInstance', dataGridInstance)

// 处理操作按钮属性
const actionProps = computed(() => props.action)

// 处理表格操作属性
const toolbarActionProps = computed(() => props.tableActions)

// 处理查询参数属性
const rangeProps = computed(() => {
  return {
    offset: props.queryParams?.offset,
    limit: props.queryParams?.limit,
    total: props.total,
  }
})

const handleDataFetch = async (query: QueryParams) => {
  // 直接调用 handleDataFetch，避免修改 queryParams 导致循环触发
  if (dataGridInstance?.handleDataFetch) {
    const currentQueryParams =
      dataGridInstance.gridOptions.value.toolbarOptions.queryParams || {}
    const mergedQueryParams = {
      ...currentQueryParams,
      offset: query.offset,
      limit: query.limit,
    }
    await dataGridInstance.handleDataFetch(mergedQueryParams)
  }
}
</script>

<style scoped>
.toolbar {
  min-height: 50px;
}
</style>
