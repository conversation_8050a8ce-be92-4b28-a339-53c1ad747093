import { ref, computed, watch, readonly } from 'vue'
import type { Ref } from 'vue'
import { SearchSuggestions } from '../../../types'
import { SearchCoreService } from '../SearchCoreService'

/**
 * 防抖工具函数 - 改进版，支持取消
 */
function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void & { cancel: () => void } {
  let timeoutId: ReturnType<typeof setTimeout> | undefined
  
  const debouncedFn = (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      timeoutId = undefined
      func(...args)
    }, wait)
  }
  
  debouncedFn.cancel = () => {
    clearTimeout(timeoutId)
    timeoutId = undefined
  }
  
  return debouncedFn as typeof debouncedFn & { cancel: () => void }
}

/**
 * 搜索建议 Composable
 * 遵循单一职责原则，专注于搜索建议管理
 */
export function useSearchSuggestions(
  columns: Ref<any[]>,
  currentInputText: Ref<string>
) {
  const suggestions = ref<SearchSuggestions[]>([])
  const isLoading = ref(false)

  /**
   * 更新搜索建议
   */
  const updateSuggestions = (columnsList: any[]) => {
    if (!Array.isArray(columnsList) || columnsList.length === 0) {
      suggestions.value = []
      return
    }

    suggestions.value = columnsList
      .filter((column) => column.operators?.includes('ilike'))
      .map((column) => ({
        field: column.field,
        fieldLabel: column.title || column.field,
        dataType: column.dataType,
        operator: 'ilike',
        operatorLabel: SearchCoreService.getOperatorLabel('ilike'),
        searchValue: currentInputText.value,
      }) as SearchSuggestions)
  }

  /**
   * 计算建议数量
   */
  const suggestionsCount = computed(() => suggestions.value.length)

  /**
   * 监听列数据变化
   */
  watch(
    columns,
    (newColumns) => {
      if (Array.isArray(newColumns) && newColumns.length > 0) {
        updateSuggestions(newColumns)
      }
    },
    { deep: true, immediate: true }
  )

  /**
   * 防抖更新搜索建议
   */
  const debouncedUpdateSuggestions = debounce((newText: string) => {
    suggestions.value.forEach(suggestion => {
      suggestion.searchValue = newText
    })
  }, 300)

  /**
   * 监听输入文本变化，使用防抖更新建议中的搜索值
   */
  const stopTextWatch = watch(currentInputText, (newText) => {
    debouncedUpdateSuggestions(newText)
  })

  /**
   * 清理函数
   */
  const cleanup = () => {
    debouncedUpdateSuggestions.cancel()
    stopTextWatch()
  }

  return {
    suggestions: readonly(suggestions),
    suggestionsCount,
    isLoading: readonly(isLoading),
    updateSuggestions,
    cleanup,
  }
}