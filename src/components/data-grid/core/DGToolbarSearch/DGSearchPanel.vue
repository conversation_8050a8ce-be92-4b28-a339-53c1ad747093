<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'
import Button from '@/components/ui/button/Button.vue'
import { SearchPanelConfig } from '../../types'
import { useSearchFavorites } from './composables/useSearchFavorites'
import type { SearchTagData } from '../../types'
import DGSearchColumns from './DGSearchColumns.vue'
import { ErrorHandler, errorHandler } from '../../utils'

interface Props {
  visible: boolean
  columns: any[]
  searchPanelConfig?: SearchPanelConfig
  searchTags?: SearchTagData[] // 新增：接收当前所有标签
  dataSource?: string // 新增：数据源标识符，用于隔离收藏项
}

interface Emits {
  close: []
  'field-select': [field: string]
  'quick-select': [item: any]
  'favorite-select': [item: any]
  'open-advanced': []
  'apply-favorite': [favId: string]
}

const props = withDefaults(defineProps<Props>(), {
  searchPanelConfig: () => ({
    showFieldList: true,
    showAdvancedSearch: true,
    quickItems: [],
    recommendedItems: [],
    favoriteItems: [],
  }),
  searchTags: () => [],
  dataSource: 'default',
})

const emit = defineEmits<Emits>()

// 收藏逻辑 - 使用数据源隔离的收藏实例
const {
  favorites,
  addFavorite,
  removeFavorite,
  applyFavorite,
  reloadFavorites,
} = useSearchFavorites(props.dataSource)

// 默认快速搜索项
const defaultQuickItems = [
  { icon: 'lucide:calendar', label: '今天创建', value: 'created_today' },
  { icon: 'lucide:clock', label: '本周', value: 'this_week' },
  { icon: 'lucide:calendar-days', label: '本月', value: 'this_month' },
  { icon: 'lucide:user', label: '我创建的', value: 'my_records' },
  { icon: 'lucide:star', label: '重要', value: 'important' },
  { icon: 'lucide:check-circle', label: '已完成', value: 'completed' },
]

// 默认收藏项
const defaultFavoriteItems = [
  {
    icon: 'lucide:bookmark',
    label: '高优先级任务',
    value: 'high_priority_tasks',
  },
  { icon: 'lucide:bookmark', label: '待审核项目', value: 'pending_approval' },
  { icon: 'lucide:bookmark', label: '本月销售数据', value: 'monthly_sales' },
]

// 计算显示的快速搜索项
const quickItems = computed(() => {
  return props.searchPanelConfig?.quickItems?.length
    ? props.searchPanelConfig.quickItems
    : defaultQuickItems
})

// 计算显示的收藏项
const favoriteItems = computed(() => {
  return props.searchPanelConfig?.favoriteItems?.length
    ? props.searchPanelConfig.favoriteItems
    : defaultFavoriteItems
})

// 计算显示的推荐项
const recommendedItems = computed(() => {
  return props.searchPanelConfig?.recommendedItems || []
})

// 可搜索的字段（用于 Suggestions）
const searchableFields = computed(() => {
  return props.columns.filter((col) => col.operators?.includes('ilike'))
})

// 所有字段（用于 SearchPanel 显示）
const allFields = computed(() => {
  return props.columns || []
})

// 按类型分组显示字段
const groupedFields = computed(() => {
  const groups = {
    searchable: [] as any[],
    numeric: [] as any[],
    date: [] as any[],
    other: [] as any[],
  }

  allFields.value.forEach((field) => {
    if (field.operators?.includes('ilike')) {
      groups.searchable.push(field)
    } else if (['number', 'integer', 'float'].includes(field.dataType)) {
      groups.numeric.push(field)
    } else if (['date', 'datetime', 'timestamp'].includes(field.dataType)) {
      groups.date.push(field)
    } else {
      groups.other.push(field)
    }
  })

  return groups
})

// 获取字段图标
const getFieldIcon = (field: any) => {
  if (field.operators?.includes('ilike')) {
    return 'lucide:type'
  } else if (['number', 'integer', 'float'].includes(field.dataType)) {
    return 'lucide:hash'
  } else if (['date', 'datetime', 'timestamp'].includes(field.dataType)) {
    return 'lucide:calendar'
  } else if (field.dataType === 'boolean') {
    return 'lucide:toggle-left'
  }
  return 'lucide:tag'
}

// 处理字段选择
const handleFieldSelect = (field: string) => {
  emit('field-select', field)
  // 不立即关闭，让用户继续输入
  // emit('close')
}

// 处理快速搜索点击
const handleQuickItemClick = (item: any) => {
  if (item.onClick) {
    item.onClick(item.value)
  }
  emit('quick-select', item)
  emit('close')
}

// 处理推荐项点击
const handleRecommendedItemClick = (item: any) => {
  if (item.onClick) {
    item.onClick(item.value)
  }
  emit('quick-select', item)
  emit('close')
}

// 处理收藏夹点击
const handleFavoriteClick = (item: any) => {
  if (item.onClick) {
    item.onClick(item.value)
  }
  emit('favorite-select', item)
  emit('close')
}

// 处理高级搜索
const handleAdvancedSearch = () => {
  emit('open-advanced')
}

// 自动生成收藏名
function getAutoFavoriteName() {
  const base = '收藏'
  const num = favorites.value.length + 1
  return `${base}${num}`
}

// 保存当前搜索为收藏
function handleSaveFavorite() {
  if (!props.searchTags || props.searchTags.length === 0) {
    errorHandler.handleWarning('当前没有搜索条件，请先添加搜索条件后再保存', {
      showToUser: true,
      logToConsole: false,
    })
    return
  }

  const customName = prompt('请输入收藏名称:', getAutoFavoriteName())
  if (!customName || customName.trim() === '') return

  const name = customName.trim()
  addFavorite(name, props.searchTags)
}

// 应用收藏
function handleApplyFavorite(favId: string) {
  // 调用组合函数获取收藏的标签数据
  const favoriteTagData = applyFavorite(favId)
  if (favoriteTagData && favoriteTagData.length > 0) {
    emit('apply-favorite', favId)
    emit('close')
  } else {
    errorHandler.handleWarning(`收藏数据为空或不存在: ${favId}`, {
      showToUser: false,
      logToConsole: true,
    })
  }
}

// 删除收藏
function handleRemoveFavorite(favId: string) {
  removeFavorite(favId)
}

// 点击外部关闭
const panelRef = ref<HTMLElement>()

const handleClickOutside = (event: Event) => {
  if (panelRef.value && !panelRef.value.contains(event.target as Node)) {
    // 检查点击的元素是否是搜索输入框或其子元素
    const target = event.target as Element
    const isSearchInput =
      target?.closest('.search-input-container') ||
      target?.closest('[class*="search-input"]') ||
      target?.tagName === 'INPUT'

    // 如果点击的是搜索输入框相关元素，不关闭面板
    if (isSearchInput) {
      return
    }

    emit('close')
  }
}

// 存储事件监听器引用以便正确清理
let handleFavoritesUpdate: ((event: CustomEvent) => void) | null = null

onMounted(() => {
  document.addEventListener('click', handleClickOutside)

  // 监听收藏更新事件
  if (typeof window !== 'undefined') {
    handleFavoritesUpdate = (event: CustomEvent) => {
      // 只有当前数据源的收藏更新时才重新加载
      if (event.detail?.dataSource === props.dataSource) {
        reloadFavorites()
      }
    }
    window.addEventListener(
      'favorites-updated',
      handleFavoritesUpdate as EventListener
    )
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)

  // 移除收藏更新事件监听
  if (typeof window !== 'undefined' && handleFavoritesUpdate) {
    window.removeEventListener('favorites-updated', handleFavoritesUpdate as EventListener)
    handleFavoritesUpdate = null
  }
})
</script>

<template>
  <div
    v-if="visible"
    ref="panelRef"
    class="absolute top-full left-0 right-0 mt-1 z-50 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden"
  >
    <div class="flex min-h-[400px]">
      <!-- 左侧：字段列表 -->
      <div
        v-if="searchPanelConfig?.showFieldList !== false"
        class="w-1/3 border-r border-gray-200 p-4"
      >
        <DGSearchColumns :columns="columns" @field-select="handleFieldSelect" />
      </div>

      <!-- 中间：可配置内容区 -->
      <div
        :class="[
          searchPanelConfig?.showFieldList !== false ? 'w-1/3' : 'w-2/3',
          'border-r border-gray-200 p-4',
        ]"
      >
        <div class="flex items-center gap-2 mb-3">
          <Icon icon="lucide:zap" class="w-4 h-4 text-green-500" />
          <h3 class="text-sm font-medium text-gray-900">
            {{ recommendedItems.length ? '推荐搜索' : '快速搜索' }}
          </h3>
        </div>
        <div class="space-y-1 max-h-[320px] overflow-y-auto">
          <!-- 显示推荐项（如果有配置） -->
          <template v-if="recommendedItems.length">
            <button
              v-for="item in recommendedItems"
              :key="item.value"
              type="button"
              class="w-full flex items-center gap-2 px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              @click="handleRecommendedItemClick(item)"
            >
              <Icon
                :icon="item.icon || 'lucide:search'"
                class="w-4 h-4 text-gray-400"
              />
              <span>{{ item.label }}</span>
            </button>
          </template>
          <!-- 否则显示默认快速搜索项 -->
          <template v-else>
            <button
              v-for="item in quickItems"
              :key="item.value"
              type="button"
              class="w-full flex items-center gap-2 px-3 py-2 text-sm text-left text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              @click="handleQuickItemClick(item)"
            >
              <Icon
                :icon="item.icon || 'lucide:search'"
                class="w-4 h-4 text-gray-400"
              />
              <span>{{ item.label }}</span>
            </button>
          </template>
        </div>
      </div>

      <!-- 右侧：个人收藏夹 -->
      <div
        :class="[
          searchPanelConfig?.showFieldList !== false ? 'w-1/3' : 'w-1/3',
          'p-4',
        ]"
      >
        <div class="flex items-center gap-2 mb-3">
          <Icon icon="lucide:heart" class="w-4 h-4 text-red-500" />
          <h3 class="text-sm font-medium text-gray-900">个人收藏</h3>
        </div>
        <div class="space-y-1 mb-4 max-h-[280px] overflow-y-auto">
          <div
            v-if="favorites.length === 0"
            class="text-xs text-gray-400 text-center py-2"
          >
            暂无收藏
          </div>
          <div
            v-for="fav in favorites"
            :key="fav.id"
            class="flex items-center gap-2 group hover:bg-gray-50 rounded-md px-2 py-1"
          >
            <button
              type="button"
              class="flex-1 text-left text-sm text-gray-700 truncate"
              @click="handleApplyFavorite(fav.id)"
            >
              <Icon
                icon="lucide:bookmark"
                class="w-4 h-4 text-gray-400 mr-1 inline"
              />
              <span>{{ fav.name }}</span>
              <span class="text-xs text-gray-400 ml-2">{{
                new Date(fav.createdAt).toLocaleDateString()
              }}</span>
            </button>
            <button
              type="button"
              class="text-xs text-gray-400 hover:text-red-500 ml-1"
              @click.stop="handleRemoveFavorite(fav.id)"
              title="删除收藏"
            >
              <Icon icon="lucide:trash" class="w-4 h-4" />
            </button>
          </div>
        </div>
        <!-- 添加收藏按钮 -->
        <button
          type="button"
          :class="[
            'w-full flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors border border-dashed',
            !props.searchTags || props.searchTags.length === 0
              ? 'text-gray-400 border-gray-200 cursor-not-allowed'
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 border-gray-300',
          ]"
          @click="handleSaveFavorite"
        >
          <Icon icon="lucide:plus" class="w-4 h-4" />
          <span>保存当前搜索</span>
          <span
            v-if="!props.searchTags || props.searchTags.length === 0"
            class="text-xs text-gray-400 ml-auto"
          >
            (需要搜索条件)
          </span>
        </button>
      </div>
    </div>

    <!-- 底部：高级搜索入口 -->
    <div
      v-if="searchPanelConfig?.showAdvancedSearch !== false"
      class="border-t border-gray-200 p-3 bg-gray-50"
    >
      <Button
        variant="ghost"
        size="sm"
        class="w-full justify-start"
        @click="handleAdvancedSearch"
      >
        <Icon icon="lucide:settings" class="w-4 h-4 mr-2" />
        高级搜索
      </Button>
    </div>
  </div>
</template>

<style scoped>
/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 0.375rem;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 0.25rem;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
