<script setup lang="ts">
import { inject, computed, ref, onMounted, onUnmounted } from 'vue'
import DGSearchInput from './DGSearchInput.vue'
import DGSearchPanel from './DGSearchPanel.vue'
import DGAdvancedSearchDialog from './DGAdvancedSearchDialog.vue'
import FieldSuggestions from './FieldSuggestions.vue'
import { SearchTagData } from '../../types'
import { FilterGroup, FilterCondition } from '@/types/api/queryParams'
import { QueryBuilder } from './QueryBuilder'
import { generateId } from './utils/common'
import { ErrorHandler, errorHandler } from '../../utils'

// Composables
import { useSearchSuggestions } from './composables/useSearchSuggestions'
import { useSearchTags } from './composables/useSearchTags'
import { useSearchKeyboard } from './composables/useSearchKeyboard'
import { useSearchOperations } from './composables/useSearchOperations'
import { useSearchFavorites } from './composables/useSearchFavorites'

// 注入 DataGrid 实例
const dataGridInstance = inject('dataGridInstance', null)

// 响应式状态
const currentInputText = ref('')
const showSuggestions = ref(false) // 字段建议下拉
const showSearchPanel = ref(false) // 搜索面板下拉
const showAdvancedDialog = ref(false)
const presetField = ref('') // 预设字段
const editingTag = ref<SearchTagData | null>(null) // 正在编辑的标签

// 组件引用
const searchInputRef = ref()

// 计算属性
const columns = computed(
  () => dataGridInstance?.gridOptions?.value?.columns || []
)
const queryParams = computed(
  () => dataGridInstance?.gridOptions?.value?.toolbarOptions?.queryParams || {}
)
const searchPanelConfig = computed(
  () => dataGridInstance?.gridOptions?.value?.toolbarOptions?.searchPanel || {}
)

// 递归平铺所有叶子条件
function flattenFilterGroup(group: any): FilterCondition[] {
  const result: FilterCondition[] = []
  if (group && Array.isArray(group.conditions)) {
    for (const cond of group.conditions) {
      if (cond && typeof cond === 'object' && 'field' in cond) {
        result.push(cond)
      } else if (cond && typeof cond === 'object' && 'conditions' in cond) {
        result.push(...flattenFilterGroup(cond))
      }
    }
  }
  return result
}

// 编辑模式下的初始数据
const initialFilters = computed(() => {
  // 如果当前是收藏TAG，递归平铺所有叶子条件
  if (favoriteTagRef.value && favoriteTagRef.value.length > 0) {
    const favTag = favoriteTagRef.value[0]
    if (favTag && favTag.underlyingFilter) {
      return flattenFilterGroup(favTag.underlyingFilter)
    }
  }
  if (!editingTag.value) return undefined
  const filter = editingTag.value.underlyingFilter
  if ('field' in filter) {
    // 单个条件
    return [filter]
  } else {
    // 条件组
    return filter.conditions.filter((c) => 'field' in c) as FilterCondition[]
  }
})

const initialCouple = computed(() => {
  if (!editingTag.value) return undefined

  const filter = editingTag.value.underlyingFilter

  if ('couple' in filter) {
    return filter.couple
  }

  return 'and'
})

// 使用 composables
const { suggestions, suggestionsCount, isLoading } = useSearchSuggestions(
  columns,
  currentInputText
)

const { searchTags, updateSearchTags } = useSearchTags(queryParams, columns)
// 新增：本地ref用于临时显示收藏TAG
const favoriteTagRef = ref<SearchTagData[] | null>(null)

const { suggestionActiveIndex, resetSuggestionState, handleKeydown } =
  useSearchKeyboard(suggestions as any, showSuggestions)

const {
  selectSuggestion,
  removeFilterFromQueryParams,
  handleInputChange,
  handleInputFocus,
  handleInputBlur,
} = useSearchOperations(
  dataGridInstance,
  showSuggestions,
  currentInputText,
  suggestionActiveIndex,
  updateSearchTags // 传入updateSearchTags函数
)

/**
 * 处理键盘事件
 */
const handleInputKeydown = (event: KeyboardEvent) => {
  const result = handleKeydown(event)

  if (result.shouldSelectSuggestion && result.selectedSuggestion) {
    selectSuggestion(result.selectedSuggestion, currentInputText.value)
  }
}

/**
 * 处理标签删除
 */
const handleRemoveTag = async (tagId: string) => {
  await removeFilterFromQueryParams(tagId, searchTags as any)
}

/**
 * 处理标签编辑
 */
const handleEditTag = (tagData: SearchTagData) => {
  // 设置正在编辑的标签
  editingTag.value = tagData

  // 关闭所有下拉
  closeAllDropdowns()

  // 打开高级搜索对话框进行编辑
  showAdvancedDialog.value = true
}

/**
 * 处理输入文本变化 - 有字符时显示建议
 */
const handleInputTextChange = (value: string) => {
  handleInputChange(value, suggestions as any)

  // 有输入文本时显示建议，关闭搜索面板
  if (value.trim()) {
    showSuggestions.value = true
    showSearchPanel.value = false
  } else {
    // 无输入文本时，如果输入框有焦点则显示搜索面板
    showSuggestions.value = false
    if (document.activeElement === searchInputRef.value?.getInputRef()) {
      showSearchPanel.value = true
    }
  }
}

/**
 * 处理输入框聚焦 - 根据输入内容决定显示哪个下拉
 */
const handleInputFocusEvent = () => {
  if (currentInputText.value.trim()) {
    showSuggestions.value = true
    showSearchPanel.value = false
  } else {
    showSuggestions.value = false
    showSearchPanel.value = true
  }
  handleInputFocus(suggestions as any)
}

/**
 * 处理输入框失焦
 */
const handleInputBlurEvent = () => {
  // 延迟关闭，允许点击下拉项
  setTimeout(() => {
    // 检查焦点是否在下拉面板内
    const activeElement = document.activeElement
    const isInSuggestions = activeElement?.closest('.search-suggestions')
    const isInPanel = activeElement?.closest('.search-panel')
    const isInputFocused = activeElement === searchInputRef.value?.getInputRef()

    // 如果焦点在下拉面板内或输入框仍有焦点，则不关闭
    if (isInSuggestions || isInPanel || isInputFocused) {
      return
    }

    // 否则关闭所有下拉
    showSuggestions.value = false
    showSearchPanel.value = false
  }, 150)
  handleInputBlur()
}

/**
 * 处理下拉按钮点击 - 显示搜索面板
 */
const handleDropdownClick = () => {
  // 下拉按钮点击时，强制显示搜索面板（无论是否有输入值）
  showSuggestions.value = false
  showSearchPanel.value = !showSearchPanel.value

  // 如果打开搜索面板，确保输入框获得焦点
  if (showSearchPanel.value) {
    searchInputRef.value?.focus()
  }
}

/**
 * 处理建议选择
 */
const handleSuggestionSelect = (suggestion: any, searchValue: string) => {
  selectSuggestion(suggestion, searchValue)
  showSuggestions.value = false
}

/**
 * 处理字段选择 - 带入高级搜索
 */
const handleFieldSelect = (field: string) => {
  // 设置预设字段
  presetField.value = field

  // 直接打开高级搜索对话框
  closeAllDropdowns()
  showAdvancedDialog.value = true
}

/**
 * 处理快速搜索选择
 */
const handleQuickSelect = async (item: any) => {
  if (
    !item?.value ||
    !dataGridInstance?.gridOptions?.value?.toolbarOptions?.queryParams
  ) {
    errorHandler.handleWarning('快速搜索项无效或缺少查询参数', {
      showToUser: false,
      logToConsole: true,
    })
    return
  }

  try {
    // 根据快速搜索项的值创建相应的筛选条件
    const filters = createQuickSearchFilters(item.value)

    if (filters.length === 0) {
      errorHandler.handleWarning(
        `无法为快速搜索项创建筛选条件: ${item.value}`,
        {
          showToUser: false,
          logToConsole: true,
        }
      )
      return
    }

    // 关闭搜索面板
    closeAllDropdowns()

    // 应用筛选条件
    await handleApplyAdvancedFilters(filters, 'and')
  } catch (error) {
    errorHandler.handleError(error, {
      message: ErrorHandler.getUserFriendlyMessage(
        ErrorHandler.MESSAGES.SEARCH_APPLY_FAILED
      ),
      type: 'error',
    })
  }
}

/**
 * 创建快速搜索筛选条件
 */
const createQuickSearchFilters = (searchType: string): FilterCondition[] => {
  const today = new Date()
  const filters: FilterCondition[] = []

  switch (searchType) {
    case 'today_customers':
      filters.push({
        id: generateId(),
        field: 'created_at',
        op: 'ge',
        value: today.toISOString().split('T')[0],
      })
      break

    case 'weekly_active':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      filters.push({
        id: generateId(),
        field: 'last_active_at',
        op: 'ge',
        value: weekAgo.toISOString().split('T')[0],
      })
      break

    case 'high_value':
      filters.push({
        id: generateId(),
        field: 'total_amount',
        op: 'gt',
        value: '10000',
      })
      break

    case 'gmail_users':
      filters.push({
        id: generateId(),
        field: 'email',
        op: 'ilike',
        value: '%@gmail.com',
      })
      break

    case 'mobile_133':
      filters.push({
        id: generateId(),
        field: 'phone',
        op: 'ilike',
        value: '133%',
      })
      break

    case 'pending_orders':
      filters.push({
        id: generateId(),
        field: 'status',
        op: 'eq',
        value: 'pending',
      })
      break

    case 'vip_customers':
      filters.push({
        id: generateId(),
        field: 'customer_level',
        op: 'eq',
        value: 'VIP',
      })
      break

    default:
      errorHandler.handleWarning(`未知的快速搜索类型: ${searchType}`, {
        showToUser: false,
        logToConsole: true,
      })
      break
  }

  return filters
}

/**
 * 处理收藏搜索选择
 */
const handleFavoriteSelect = async (item: any) => {
  if (
    !item?.id ||
    !dataGridInstance?.gridOptions?.value?.toolbarOptions?.queryParams
  ) {
    errorHandler.handleWarning('收藏搜索项无效或缺少查询参数', {
      showToUser: false,
      logToConsole: true,
    })
    return
  }

  try {
    // 关闭搜索面板
    closeAllDropdowns()

    // 应用收藏搜索条件
    await handleApplyFavorite(item.id)
  } catch (error) {
    errorHandler.handleError(error, {
      message: ErrorHandler.getUserFriendlyMessage(
        ErrorHandler.MESSAGES.SEARCH_FAVORITE_LOAD_FAILED
      ),
      type: 'error',
    })
  }
}

/**
 * 关闭所有下拉
 */
const closeAllDropdowns = () => {
  showSuggestions.value = false
  showSearchPanel.value = false
}

/**
 * 打开高级搜索对话框
 */
const handleOpenAdvancedSearch = () => {
  closeAllDropdowns()
  showAdvancedDialog.value = true
}

/**
 * 关闭高级搜索对话框
 */
const handleCloseAdvancedSearch = () => {
  showAdvancedDialog.value = false
  // 清除预设字段
  presetField.value = ''
  // 清除编辑状态
  editingTag.value = null
}

/**
 * 处理添加标签 - 来自高级搜索对话框的复合标签
 */
const handleAddTag = (tagData: SearchTagData) => {
  // 在编辑模式下，需要替换现有标签
  if (editingTag.value) {
    // 手动触发标签更新以反映编辑后的结果
    updateSearchTags()
  } else {
    // 新增模式下，标签会由 updateSearchTags() 自动生成
    // 这里不需要手动添加，因为 applyFilters 已经处理了数据更新
    updateSearchTags()
  }
}

/**
 * 应用高级搜索筛选 - 使用新的QueryBuilder
 */
const handleApplyAdvancedFilters = async (
  filters: FilterCondition[],
  couple: 'and' | 'or'
) => {
  if (!dataGridInstance?.gridOptions?.value?.toolbarOptions?.queryParams) {
    errorHandler.handleError(
      ErrorHandler.getUserFriendlyMessage(
        ErrorHandler.MESSAGES.QUERY_PARAMS_NOT_FOUND
      ),
      {
        type: 'error',
      }
    )
    return
  }

  try {
    const queryParams =
      dataGridInstance.gridOptions.value.toolbarOptions.queryParams

    if (filters.length === 0) {
      return
    }

    if (editingTag.value) {
      const tagFilter = editingTag.value.underlyingFilter
      const targetId = tagFilter.id!

      if ('field' in tagFilter) {
        // 编辑单个条件
        QueryBuilder.editConditionToGroup(
          queryParams,
          targetId,
          filters,
          couple
        )
      } else {
        // 编辑条件组
        QueryBuilder.editGroup(queryParams, targetId, filters, couple)
      }
    } else {
      // 添加新的条件或条件组
      QueryBuilder.addGroup(queryParams, filters, couple)
    }

    // 重置分页到第一页
    queryParams.offset = 0

    // 刷新数据
    await dataGridInstance.refreshData()

    // 手动触发标签更新
    updateSearchTags()
  } catch (error) {
    errorHandler.handleError(error, {
      message: ErrorHandler.getUserFriendlyMessage(
        ErrorHandler.MESSAGES.SEARCH_APPLY_FAILED
      ),
      type: 'error',
    })
  }
}

// 收藏逻辑 - 使用数据源隔离
const dataSource = computed(() => dataGridInstance?.moduleModel || 'default')
const { favorites, applyFavorite, findFavorite, reloadFavorites } =
  useSearchFavorites(dataSource.value)

// 监听收藏更新事件
// 存储事件监听器引用以便正确清理
let handleFavoritesUpdate: ((event: CustomEvent) => void) | null = null

onMounted(() => {
  if (typeof window !== 'undefined') {
    handleFavoritesUpdate = (event: CustomEvent) => {
      // 只有当前数据源的收藏更新时才重新加载
      if (event.detail?.dataSource === dataSource.value) {
        reloadFavorites()
      }
    }
    window.addEventListener(
      'favorites-updated',
      handleFavoritesUpdate as EventListener
    )
  }
})

onUnmounted(() => {
  if (typeof window !== 'undefined' && handleFavoritesUpdate) {
    window.removeEventListener('favorites-updated', handleFavoritesUpdate as EventListener)
    handleFavoritesUpdate = null
  }
})

// 处理应用收藏事件
const handleApplyFavorite = async (favId: string) => {
  try {
    const fav = findFavorite(favId)
    if (!fav) {
      errorHandler.handleWarning(`收藏项不存在: ${favId}`, {
        showToUser: false,
        logToConsole: true,
      })
      return
    }

    if (!dataGridInstance?.gridOptions?.value?.toolbarOptions?.queryParams) {
      errorHandler.handleWarning('dataGridInstance 或 queryParams 不存在', {
        showToUser: false,
        logToConsole: true,
      })
      return
    }

    // 清空当前的搜索条件和收藏标签
    favoriteTagRef.value = null

    // 提取收藏中的所有过滤条件
    const filters = fav.tags.map((tag) => tag.underlyingFilter)

    // 构建新的过滤条件组
    const newFilters: FilterGroup = {
      id: `fav_group_${Date.now()}`,
      couple: 'and' as const,
      conditions: filters,
    }

    // 更新查询参数 - 使用新的对象引用确保Vue响应式触发
    const queryParams =
      dataGridInstance.gridOptions.value.toolbarOptions.queryParams
    queryParams.filters = newFilters
    queryParams.offset = 0

    // 刷新数据
    await dataGridInstance.refreshData()

    // 强制更新搜索标签显示 - 使用nextTick确保DOM更新
    setTimeout(() => {
      updateSearchTags()
    }, 50)
  } catch (error) {
    errorHandler.handleError(error, {
      message: ErrorHandler.getUserFriendlyMessage(
        ErrorHandler.MESSAGES.SEARCH_FAVORITE_LOAD_FAILED
      ),
      type: 'error',
    })
  }
}
</script>

<template>
  <div v-bind="$attrs" class="relative w-full">
    <!-- 搜索输入区域 -->
    <div class="relative w-full">
      <DGSearchInput
        ref="searchInputRef"
        v-model="currentInputText"
        :suggestions-count="suggestionsCount"
        :search-tags="
          favoriteTagRef
            ? JSON.parse(JSON.stringify(favoriteTagRef))
            : searchTags
        "
        @focus="handleInputFocusEvent"
        @blur="handleInputBlurEvent"
        @keydown="handleInputKeydown"
        @remove-tag="handleRemoveTag"
        @edit-tag="handleEditTag"
        @update:modelValue="handleInputTextChange"
        @dropdown-click="handleDropdownClick"
      />
    </div>

    <!-- 字段建议下拉 - 当有输入内容时显示 -->
    <div
      v-if="showSuggestions"
      class="search-suggestions absolute top-full left-0 right-0 mt-1 z-50 bg-white border border-gray-200 rounded-lg shadow-lg"
    >
      <FieldSuggestions
        :search-text="currentInputText"
        :suggestions="suggestions as any"
        :active-index="suggestionActiveIndex"
        :is-loading="isLoading"
        @select="handleSuggestionSelect"
      />
    </div>

    <!-- 搜索面板 - 当没有输入内容且点击下拉时显示 -->
    <DGSearchPanel
      class="search-panel"
      :visible="showSearchPanel"
      :columns="columns"
      :search-panel-config="searchPanelConfig"
      :search-tags="JSON.parse(JSON.stringify(searchTags))"
      :data-source="dataSource"
      @close="closeAllDropdowns"
      @field-select="handleFieldSelect"
      @quick-select="handleQuickSelect"
      @favorite-select="handleFavoriteSelect"
      @open-advanced="handleOpenAdvancedSearch"
      @apply-favorite="handleApplyFavorite"
    />

    <!-- 高级搜索对话框 -->
    <DGAdvancedSearchDialog
      :visible="showAdvancedDialog"
      :columns="columns"
      :preset-field="presetField"
      :initial-filters="initialFilters"
      :initial-couple="initialCouple"
      @close="handleCloseAdvancedSearch"
      @apply="(filters, couple) => handleApplyAdvancedFilters(filters, couple)"
      @add-tag="handleAddTag"
    />
  </div>
</template>
