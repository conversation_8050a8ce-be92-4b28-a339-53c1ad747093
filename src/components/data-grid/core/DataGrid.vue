<template>
  <div
    class="flex flex-col overflow-hidden relative"
    :style="containerStyle"
    ref="containerRef"
  >
    <DGToolbar
      v-if="toolbarOptions"
      v-bind="toolbarOptions"
      ref="toolbarRef"
      class="flex-none"
      v-memo="[
        toolbarOptions?.title,
        toolbarOptions?.total,
        toolbarOptions?.queryParams?.offset,
        toolbarOptions?.queryParams?.limit,
      ]"
    />
    <div class="flex-1 min-h-0 relative" ref="gridContainerRef">
      <vxe-grid
        v-bind="processedGridOptionsWithoutToolbar"
        height="100%"
        ref="gridRef"
        v-on="gridEvents"
        v-memo="[
          processedGridOptionsWithoutToolbar.columns,
          processedGridOptionsWithoutToolbar.data,
          processedGridOptionsWithoutToolbar.loading,
        ]"
      >
        <template
          v-for="(slotInfo, slotName) in customSlots"
          :key="slotName"
          #[slotName]="params"
        >
          <slot :name="slotName" v-bind="params">
            <template v-if="isComponentRender(slotInfo, params)">
              <component
                :is="getComponentType(slotInfo, params)"
                v-bind="getComponentProps(slotInfo, params)"
              />
            </template>
            <div v-else v-html="renderSlotWithConfig(slotInfo, params)"></div>
          </slot>
        </template>
      </vxe-grid>

      <DataGridLoading :visible="isLoading" :config="loadingConfig" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  provide,
  shallowRef,
  onBeforeUnmount,
  markRaw,
} from 'vue'
import { useResizeObserver } from '@vueuse/core'
import type { DataGridProps } from '../types'
import type { UseDataGridReturn } from '../types'
import DGToolbar from './DGToolbar.vue'
import DataGridLoading from './DataGridLoading.vue'
import { getGlobalPluginManager } from '../plugins'
import { performanceMonitor } from '../utils/performanceMonitor'
import { buildStatusMapFromMetadata } from '../utils/columnHelpers'

// DataGrid 组件属性定义
interface DataGridPropsWithInstance extends DataGridProps {
  // 数据网格实例，通过 useDataGrid 创建
  dataGridInstance?: UseDataGridReturn
}
const props = withDefaults(defineProps<DataGridPropsWithInstance>(), {
  moduleModel: undefined,
  gridOptions: () => ({}),
  dataGridInstance: undefined,
})

// 提供实例给子组件 & 初始化插件系统
provide('dataGridInstance', props.dataGridInstance)
const pluginManager = getGlobalPluginManager()

// 简化组件缓存
const cachedComponentTypes = shallowRef(new Map<string, unknown>())

const initializeComponents = () => {
  const components = new Map<string, unknown>()
  
  // 缓存常用组件
  const componentNames = ['StatusRenderer', 'LinkRenderer', 'BooleanRenderer', 'ActionsRenderer']
  componentNames.forEach((name) => {
    const component = pluginManager.getComponent(name)
    if (component) {
      components.set(name, component)
    }
  })
  
  // 添加默认组件
  const defaultComponent = markRaw({ render: () => null })
  components.set('__fallback__', defaultComponent)
  components.set('__empty__', defaultComponent)
  
  cachedComponentTypes.value = components
}

onMounted(initializeComponents)

// 性能监控
const componentId = computed(
  () => props.dataGridInstance?.moduleModel || props.moduleModel || 'unknown'
)

if (import.meta.env.DEV) {
  onMounted(() =>
    performanceMonitor.startComponentMonitoring(componentId.value)
  )
}

const gridRef = ref()
const toolbarRef = ref()
const containerRef = ref()

const gridContainerRef = ref()

// 响应式高度状态
const viewportHeight = shallowRef(window.innerHeight)

// 智能获取有效的 gridOptions
const effectiveGridOptions = computed(() => {
  if (props.gridOptions && Object.keys(props.gridOptions).length > 0) {
    return props.gridOptions
  }

  if (props.dataGridInstance?.gridOptions?.value) {
    return props.dataGridInstance.gridOptions.value
  }

  return {}
})

// 提取配置
const toolbarOptions = computed(() => effectiveGridOptions.value.toolbarOptions)
const isLoading = computed(() => effectiveGridOptions.value.loading || false)
const loadingConfig = computed(() => ({
  type: 'spinner' as const,
  text: '数据加载中...',
  size: 'medium' as const,
  color: 'primary' as const,
  theme: 'auto' as const,
  ...effectiveGridOptions.value.loadingConfig,
}))

// 计算自动高度
const calculateAutoHeight = () => {
  if (!containerRef.value) return 600
  const rect = containerRef.value.getBoundingClientRect()
  const availableHeight = viewportHeight.value - rect.top - 20
  return Math.max(availableHeight, 300)
}

// 计算容器样式
const containerStyle = computed(() => {
  const height = effectiveGridOptions.value.height

  if (height && height !== 'auto') {
    return {
      height:
        typeof height === 'number' || !isNaN(Number(height))
          ? `${height}px`
          : height,
    }
  }

  return { height: `${calculateAutoHeight()}px` }
})


// 简化视窗高度更新
const updateViewportHeight = () => {
  viewportHeight.value = window.innerHeight
}

useResizeObserver(containerRef, () => {
  nextTick(updateViewportHeight)
})

const gridEvents = computed(() => effectiveGridOptions.value.gridEvents || {})

// 处理后的网格选项（移除工具栏）
const processedGridOptionsWithoutToolbar = computed(() => {
  const { toolbarOptions, height, loading, loadingConfig, ...gridOptions } =
    processedGridOptions.value
  return {
    ...gridOptions,
    autoResize: false,
    syncResize: true,
    keepSource: false,
    loading: false,
  }
})

// 简化插槽名称生成
const generateSlotName = (col: unknown, slotType: string): string => {
  const field = (col as any)?.field || (col as any)?.type || 'col'
  return `${field}_${slotType}`
}

onBeforeUnmount(() => {
  cachedComponentTypes.value.clear()
})


const processColumnsRecursively = (
  cols: unknown[],
  processor: (col: unknown) => void
) => {
  cols.forEach((col) => {
    processor(col)
    const colObj = col as Record<string, unknown>
    if (colObj.children && Array.isArray(colObj.children)) {
      processColumnsRecursively(colObj.children, processor)
    }
  })
}

// 简化插槽映射提取
const customSlots = computed(() => {
  const columns = effectiveGridOptions.value.columns || []
  if (!Array.isArray(columns) || columns.length === 0) return {}

  const slots: Record<string, {
    slotName: string
    config: unknown
    field: string
    slotType: string
    column: unknown
  }> = {}

  processColumnsRecursively(columns, (col) => {
    const colObj = col as Record<string, unknown>
    if (colObj.slots) {
      const slotObj = colObj.slots as Record<string, unknown>
      Object.keys(slotObj).forEach((slotType) => {
        const slotConfig = slotObj[slotType]
        if (slotConfig && typeof slotConfig === 'object') {
          const slotName = generateSlotName(col, slotType)
          slots[slotName] = {
            slotName,
            config: slotConfig,
            field: (colObj.field as string) || (colObj.type as string),
            slotType,
            column: col,
          }
        }
      })
    }
  })

  return slots
})

// 处理列配置，将插槽配置转换为 vxe-table 格式
const processedGridOptions = computed(() => {
  const options = { ...effectiveGridOptions.value }
  if (!options.columns) return options

  const processColumns = (cols: unknown[]): unknown[] => {
    return cols.map((col) => {
      const colObj = col as Record<string, unknown>
      const processedCol = { ...colObj }

      if (colObj.slots) {
        const vxeSlots: Record<string, string> = {}
        const slotObj = colObj.slots as Record<string, unknown>
        Object.keys(slotObj).forEach((slotType) => {
          const slotConfig = slotObj[slotType]
          if (slotConfig) {
            vxeSlots[slotType] =
              typeof slotConfig === 'string'
                ? slotConfig
                : generateSlotName(col, slotType)
          }
        })
        processedCol.slots = vxeSlots
      }

      if (colObj.children && Array.isArray(colObj.children)) {
        processedCol.children = processColumns(colObj.children)
      }

      return processedCol
    })
  }

  options.columns = processColumns(options.columns)
  return options
})

// 使用插槽配置渲染内容
const renderSlotWithConfig = (
  slotInfo: { slotName: string; config: unknown; field: string; slotType: string },
  params: Record<string, unknown>
) => {
  const { config, field } = slotInfo
  if (!config) return params.value || ''

  const configObj = config as Record<string, unknown>
  const renderParams = {
    ...params,
    value: (params.row as Record<string, unknown>)?.[field] || params.value,
    field,
  }

  try {
    // ColumnHelper 生成的配置：slots.default.render
    const slotsConfig = configObj.slots as Record<string, unknown>
    const defaultSlot = slotsConfig?.default as Record<string, unknown>
    if (defaultSlot?.render && typeof defaultSlot.render === 'function') {
      return (defaultSlot.render as Function)(renderParams) || params.value || ''
    }

    // 向后兼容：直接 render 函数
    if (typeof configObj.render === 'function') {
      return (configObj.render as Function)(renderParams) || params.value || ''
    }

    return params.value || ''
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error(`[DataGrid] 插槽渲染失败 (${slotInfo.slotName}):`, error)
    }
    return params.value || ''
  }
}

const isComponentRender = (slotInfo: unknown, params: Record<string, unknown>) => {
  const slotObj = slotInfo as { slotName: string; config: unknown; field: string; slotType: string }
  const result = renderSlotWithConfig(slotObj, params)
  return typeof result === 'string' && result.startsWith('__COMPONENT__:')
}

// 获取组件类型和属性的统一处理
const getComponentInfo = (slotInfo: unknown, params: Record<string, unknown>) => {
  const slotObj = slotInfo as { slotName: string; config: unknown; field: string; slotType: string; column: unknown }
  const result = renderSlotWithConfig(slotObj, params)

  if (typeof result === 'string' && result.startsWith('__COMPONENT__:')) {
    try {
      const [, componentName, ...propsParts] = result.split(':')
      const componentProps = JSON.parse(propsParts.join(':'))

      // 从预缓存的组件类型中获取
      const componentType =
        cachedComponentTypes.value.get(componentName) ||
        cachedComponentTypes.value.get('__fallback__')

      // 如果有 configId，从简单配置存储获取完整配置
      const finalProps = componentProps.configId
        ? {
            ...componentProps,
            config:
              (window as any).__simpleConfigStore?.get(
                componentProps.configId
              ) || componentProps.config,
          }
        : componentProps

      // 添加列信息和字段信息，供组件使用（如 StatusRenderer 需要 enumInfo）
      const enhancedProps = {
        ...finalProps,
        column: slotObj.column, // 传递完整的列配置
        field: slotObj.field, // 传递字段名
        row: params.row, // 传递行数据
        value: (params.row as Record<string, unknown>)?.[slotObj.field] || params.value, // 传递当前值
      }

      // 特殊处理 StatusRenderer：预处理 statusMap
      if (
        componentName === 'StatusRenderer' &&
        (finalProps as any).config?.autoFromMetadata
      ) {
        const columnObj = slotObj.column as Record<string, unknown>
        const processedStatusMap = buildStatusMapFromMetadata(
          columnObj?.enumInfo,
          (finalProps as any).config?.statusMap
        )
        enhancedProps.config = {
          ...(finalProps as any).config,
          statusMap: processedStatusMap,
          autoFromMetadata: false, // 已经处理过了，避免重复处理
        }
      }

      return { type: componentType, props: enhancedProps }
    } catch (error) {
      console.error('解析组件信息失败:', error)
      return {
        type: cachedComponentTypes.value.get('__fallback__'),
        props: {},
      }
    }
  }

  return {
    type: cachedComponentTypes.value.get('__empty__'),
    props: {},
  }
}

const getComponentType = (slotInfo: unknown, params: Record<string, unknown>) =>
  getComponentInfo(slotInfo, params).type
const getComponentProps = (slotInfo: unknown, params: Record<string, unknown>) =>
  getComponentInfo(slotInfo, params).props

onMounted(() => {
  if (props.dataGridInstance && gridRef.value) {
    props.dataGridInstance._setGridRef(gridRef.value)
  }
  window.addEventListener('resize', updateViewportHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateViewportHeight)
})

watch(gridRef, (newGridRef) => {
  if (props.dataGridInstance && newGridRef) {
    props.dataGridInstance._setGridRef(newGridRef)
  }
})

// 暴露 vxe-grid 实例的方法给父组件
defineExpose({
  // 直接暴露 vxe-grid 实例
  getGridInstance: () => gridRef.value,
  // 暴露 ModelApi 实例
  getModelApi: () => props.dataGridInstance?.modelApi.value,
  // 暴露常用的 vxe-grid 方法
  getCheckboxRecords: () => gridRef.value?.getCheckboxRecords(),
  getRadioRecord: () => gridRef.value?.getRadioRecord(),
  getCurrentRecord: () => gridRef.value?.getCurrentRecord(),
  setCheckboxRow: (rows: unknown, checked: boolean) =>
    gridRef.value?.setCheckboxRow(rows, checked),
  setAllCheckboxRow: (checked: boolean) =>
    gridRef.value?.setAllCheckboxRow(checked),
  clearCheckboxRow: () => gridRef.value?.clearCheckboxRow(),
  clearCurrentRow: () => gridRef.value?.clearCurrentRow(),
  // 修复刷新方法，使用正确的 vxe-grid 方法
  refreshData: () => {
    if (props.dataGridInstance && props.dataGridInstance.refreshData) {
      // 调用 useDataGrid 中的 refreshData 方法
      props.dataGridInstance.refreshData()
    } else if (gridRef.value) {
      // 备用方案：使用 vxe-grid 的方法
      if (typeof gridRef.value.commitData === 'function') {
        gridRef.value.commitData()
      }
      // 或者重新设置数据来触发刷新
      const currentData = gridRef.value.getData()
      if (currentData) {
        gridRef.value.setData(currentData)
      }
    }
  },
  // 添加新的统一选择方法
  getSelection: () => props.dataGridInstance?.getSelection() || [],
  clearSelection: () => props.dataGridInstance?.clearSelection(),
  setSelection: (
    rows: unknown | unknown[],
    checked?: boolean,
    selectionType?: 'checkbox' | 'radio' | 'seq'
  ) => props.dataGridInstance?.setSelection(rows, checked, selectionType),
  setAllSelection: (checked?: boolean) =>
    props.dataGridInstance?.setAllSelection(checked),
  getSelectionCount: () => props.dataGridInstance?.getSelectionCount() || 0,
  hasSelection: () => props.dataGridInstance?.hasSelection() || false,
  // 可以根据需要添加更多方法
})
</script>

<style scoped>
/* DataGrid Footer 统计行样式 */
:deep(.vxe-table .vxe-table--footer-wrapper) {
  .vxe-footer--row {
    background-color: #fafafa;
    border-top: 2px solid #e8e8e8;
    font-weight: 500;

    .vxe-footer--column {
      color: #333;

      &:first-child {
        font-weight: bold;
      }
    }
  }
}

/* 支持自定义背景色 */
:deep(.vxe-table .vxe-table--footer-wrapper .vxe-footer--row[data-custom-bg]) {
  background-color: var(--footer-bg-color, #fafafa);
}

/* 支持固定 footer */
:deep(.vxe-table.is--footer-sticky .vxe-table--footer-wrapper) {
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 响应式样式 */
@media (max-width: 768px) {
  :deep(.vxe-table .vxe-table--footer-wrapper) {
    .vxe-footer--row {
      font-size: 12px;
    }
  }
}
</style>
