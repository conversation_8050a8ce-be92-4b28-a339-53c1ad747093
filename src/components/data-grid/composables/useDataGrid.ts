import { ref, onUnmounted, isRef, computed } from 'vue'
import { DEFAULT_GRID_OPTIONS } from '../constants'
import {
  columnHel<PERSON>,
  selectionHel<PERSON>,
  <PERSON>rror<PERSON><PERSON><PERSON>,
  errorHandler,
} from '../utils'
import { TypeValidators, TypeCheckers } from '../utils/typeGuards'
import { ApiService } from '@/api/apiService'
import { dataHelpers } from '../utils/dataHelpers'
import { createFooterMethod } from '../utils/footerHelpers'
import type { GridOptions, UseDataGridReturn, GridEvents } from '../types'
import type { VxeGridInstance } from 'vxe-table'
import type { ModelApi } from '@/api/apiService'
import { mergeHelpers } from '../utils/mergeHelpers'
import type { QueryParams } from '@/types/api/queryParams'
import { getGlobalPluginManager } from '../plugins'

/**
 * 简化的缓存系统
 */
interface CacheEntry<T> {
  value: T
  timestamp: number
}

class SimpleCache<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private readonly maxSize = 50
  private readonly maxAge = 5 * 60 * 1000 // 5分钟

  get(key: string): T | undefined {
    const entry = this.cache.get(key)
    if (!entry) return undefined

    // 检查过期
    if (Date.now() - entry.timestamp > this.maxAge) {
      this.cache.delete(key)
      return undefined
    }

    return entry.value
  }

  set(key: string, value: T): void {
    // 如果缓存已满，清理最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      if (firstKey) this.cache.delete(firstKey)
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
    })
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    // 检查是否过期
    if (Date.now() - entry.timestamp > this.maxAge) {
      this.cache.delete(key)
      return false
    }
    return true
  }

  get size(): number {
    return this.cache.size
  }
}

const apiCache = new SimpleCache<Promise<ModelApi | null>>()
const columnCache = new SimpleCache<unknown[]>()

/**
 * 跟踪活跃的组件实例，用于智能缓存清理
 */
const activeInstances = new Set<string>()

/**
 * 清理指定模块的缓存
 */
export function clearCacheForModule(moduleModel: string): void {
  apiCache.delete(moduleModel)
  columnCache.delete(moduleModel)
}

/**
 * 智能缓存清理
 */
export function smartClearCache(moduleModel: string): void {
  // 移除指定模块的缓存
  apiCache.delete(moduleModel)
  columnCache.delete(moduleModel)
  activeInstances.delete(moduleModel)

  // 如果没有活跃实例，清理所有缓存
  if (activeInstances.size === 0) {
    apiCache.clear()
    columnCache.clear()
  }
}

/**
 * 创建数据网格实例的核心组合式函数
 *
 * @param moduleModel - 数据模块的唯一标识符，用于API调用和缓存管理
 * @param gridOptions - 网格配置选项，包含工具栏、列、数据等配置
 * @returns 数据网格实例对象，包含所有网格操作方法和响应式数据
 *
 * @example
 * ```typescript
 * // 基础用法
 * const userGrid = useDataGrid('user', {
 *   enableSelection: 'checkbox',
 *   toolbarOptions: {
 *     title: '用户列表'
 *   }
 * })
 *
 * // 监听选择变化
 * userGrid.on({
 *   'selection-change': ({ selection, count }) => {
 *     console.log(`选择了 ${count} 项`)
 *   }
 * })
 * ```
 *
 * @throws {Error} 当 moduleModel 为空字符串时抛出错误
 * @throws {TypeError} 当 gridOptions 类型验证失败时在开发环境输出警告
 *
 * @since 2.0.0
 */
export function useDataGrid(
  moduleModel: string,
  gridOptions: GridOptions
): UseDataGridReturn {
  // 运行时类型检查（仅在开发环境）
  if (import.meta.env.DEV) {
    if (!TypeCheckers.isString(moduleModel) || moduleModel.trim() === '') {
      throw new Error('[useDataGrid] moduleModel must be a non-empty string')
    }

    const validation = TypeValidators.validateGridOptions(gridOptions)
    if (!validation.isValid) {
      console.warn(
        '[useDataGrid] Grid options validation warnings:',
        validation.errors
      )
    }
  }

  // 注册活跃实例
  activeInstances.add(moduleModel)

  // 获取或创建插件管理器（插件管理器会自动注册所有需要的渲染器）
  const pluginManager = getGlobalPluginManager()

  // 从传入的选项中提取 total，因为 deepMerge 可能会破坏 Ref 的结构
  const initialTotal = gridOptions.toolbarOptions?.total

  const gridOptionsRef = ref<GridOptions>(
    mergeHelpers.deepMerge(DEFAULT_GRID_OPTIONS, gridOptions)
  )

  // 确保每个实例都有独立的 total ref，避免多个网格实例共享状态
  if (gridOptionsRef.value.toolbarOptions) {
    if (isRef(initialTotal)) {
      // 如果用户传入的是 Ref，以此为准，并创建新 Ref 以避免跨实例共享
      ;(gridOptionsRef.value.toolbarOptions as any).total = ref(
        initialTotal.value
      )
    } else {
      // 如果用户传入的是 number 或 undefined，则创建一个新的 ref
      // 如果用户没传，mergedTotal 会是来自 DEFAULT_GRID_OPTIONS 的值
      const mergedTotal = gridOptionsRef.value.toolbarOptions.total
      const valueToRef =
        typeof initialTotal === 'number'
          ? initialTotal
          : typeof mergedTotal === 'number'
            ? mergedTotal
            : 0
      ;(gridOptionsRef.value.toolbarOptions as any).total = ref(valueToRef)
    }
  }

  // 创建 ModelApi 实例引用
  const modelApiRef = ref<ModelApi | null>(null)

  // 创建 loading 状态引用
  const loadingRef = ref<boolean>(false)

  // 同步 loading 状态到 gridOptions
  const syncLoadingToGridOptions = () => {
    gridOptionsRef.value.loading = loadingRef.value
  }

  // 设置 loading 状态的方法
  const setLoading = (loading: boolean) => {
    loadingRef.value = loading
    syncLoadingToGridOptions()
  }

  // 创建grid实例引用
  const gridRef = ref<VxeGridInstance>()

  // 创建 footer 方法的计算属性
  const footerMethod = computed(() => {
    const footerConfig = gridOptionsRef.value.footerConfig
    if (!footerConfig?.enabled) {
      return undefined
    }
    return createFooterMethod(footerConfig)
  })

  // 同步 footerMethod 到 gridOptions
  const syncFooterToGridOptions = () => {
    const method = footerMethod.value
    if (method) {
      gridOptionsRef.value.showFooter = true
      gridOptionsRef.value.footerMethod = method
    } else {
      gridOptionsRef.value.showFooter = false
      gridOptionsRef.value.footerMethod = undefined
    }
  }

  // 初始化时同步 footer 配置
  syncFooterToGridOptions()

  // 创建事件处理器对象 - 使用具体类型而非 any
  const gridEvents = ref<Partial<GridEvents>>({})

  // 自定义事件处理器
  const customEvents = ref<Partial<GridEvents>>({})

  // 资源管理
  const cleanupTasks = ref<(() => void)[]>([])
  const abortController = ref<AbortController | null>(null)
  const debouncedTimeouts = ref<Map<string, ReturnType<typeof setTimeout>>>(
    new Map()
  )

  // 创建防抖函数
  const createDebouncedFunction = <T extends (...args: unknown[]) => unknown>(
    key: string,
    fn: T,
    delay: number
  ): T => {
    return ((...args: Parameters<T>) => {
      // 清除之前的定时器
      const existing = debouncedTimeouts.value.get(key)
      if (existing) {
        clearTimeout(existing)
      }

      // 创建新的定时器
      const timeoutId = setTimeout(() => {
        fn(...args)
        debouncedTimeouts.value.delete(key)
      }, delay)

      debouncedTimeouts.value.set(key, timeoutId)
    }) as T
  }

  // 存储上次的选择状态，用于避免重复触发事件
  let lastSelectionState = {
    count: 0,
    hasSelection: false,
    selection: [] as unknown[],
  }

  // 触发自定义事件的辅助函数
  const emitCustomEvent = (eventName: string, payload: unknown) => {
    const handler = customEvents.value[eventName]
    if (handler && typeof handler === 'function') {
      handler(payload)
    }
  }

  // 触发选择变化相关的自定义事件（带状态检查）
  const emitSelectionEvents = () => {
    const selection = selectionHelpers.getCheckboxRadioSelection(gridRef)
    const count = selection.length
    const hasSelection = count > 0

    // 只有状态真正改变时才触发事件
    if (
      lastSelectionState.count !== count ||
      lastSelectionState.hasSelection !== hasSelection
    ) {
      lastSelectionState = { count, hasSelection, selection }

      emitCustomEvent('selection-change', { selection, count })
      emitCustomEvent('has-selection', { hasSelection })
      emitCustomEvent('selection-count-change', { count })
    }
  }

  const on = (events: Record<string, unknown>) => {
    const customEventNames = [
      'has-selection',
      'selection-change',
      'selection-clear',
      'selection-count-change',
    ]

    // 分离事件类型
    const vxeEvents: Record<string, unknown> = {}
    const customEventHandlers: Record<string, unknown> = {}

    Object.entries(events).forEach(([eventName, handler]) => {
      if (customEventNames.includes(eventName)) {
        customEventHandlers[eventName] = handler
      } else {
        vxeEvents[eventName] = handler
      }
    })

    // 如果有选择相关的自定义事件，包装相关的原生事件
    const hasSelectionEvents = Object.keys(customEventHandlers).some((name) =>
      ['has-selection', 'selection-change', 'selection-count-change'].includes(
        name
      )
    )

    if (hasSelectionEvents) {
      const wrapEvent = (eventName: string) => {
        const original = vxeEvents[eventName] as
          | ((params: unknown) => void)
          | undefined
        vxeEvents[eventName] = (params: unknown) => {
          original?.(params)
          emitSelectionEvents()
        }
      }

      wrapEvent('checkbox-change')
      wrapEvent('checkbox-all')
      wrapEvent('radio-change')
      wrapEvent('current-change')
    }

    // 存储事件
    Object.assign(gridEvents.value, vxeEvents)
    Object.assign(customEvents.value, customEventHandlers)
  }

  // 自动将事件处理器添加到 gridOptions
  gridOptionsRef.value.gridEvents = gridEvents.value

  if (moduleModel) {
    // 创建新的 AbortController
    abortController.value = new AbortController()

    /**
     * 通用API初始化函数 - 复用逻辑
     */
    const initializeModelApi = async (): Promise<ModelApi | null> => {
      // 检查是否已被取消
      if (abortController.value?.signal.aborted) {
        return null
      }

      // 检查缓存
      let apiPromise = apiCache.get(moduleModel)
      if (!apiPromise) {
        apiPromise = ApiService.getApi(moduleModel)
        apiCache.set(moduleModel, apiPromise)
      }

      const modelApi = await apiPromise
      if (!modelApi) {
        throw new Error(`Model API for "${moduleModel}" not found`)
      }

      // 再次检查是否已被取消
      if (abortController.value?.signal.aborted) {
        return null
      }

      // 设置 ModelApi 实例引用
      if (!modelApiRef.value) {
        modelApiRef.value = modelApi
      }

      return modelApi
    }

    // 异步初始化列配置，但不阻塞函数返回
    const initializeColumns = async () => {
      try {
        const modelApi = await initializeModelApi()
        if (!modelApi) return

        // 检查列配置缓存
        const cachedColumns = columnCache.get(moduleModel)
        if (cachedColumns) {
          gridOptionsRef.value.columns = cachedColumns
        } else {
          await columnHelpers.buildDataGridColumns(gridOptionsRef, modelApi)
          // 缓存列配置
          if (gridOptionsRef.value.columns) {
            columnCache.set(moduleModel, gridOptionsRef.value.columns)
          }
        }
      } catch (error) {
        // 如果是取消操作，不显示错误
        if (error instanceof Error && error.name === 'AbortError') {
          return
        }

        errorHandler.handleError(error, {
          message: ErrorHandler.getUserFriendlyMessage(
            ErrorHandler.MESSAGES.GRID_INIT_FAILED
          ),
          type: 'error',
        })
      }
    }

    const initializeData = async () => {
      try {
        setLoading(true) // 开始加载
        const modelApi = await initializeModelApi()
        if (!modelApi) return

        // 获取数据并更新 gridOptionsRef
        await dataHelpers.getDataGridData(gridOptionsRef, modelApi)
      } catch (error) {
        // 如果是取消操作，不显示错误
        if (error instanceof Error && error.name === 'AbortError') {
          return
        }

        errorHandler.handleError(error, {
          message: ErrorHandler.getUserFriendlyMessage(
            ErrorHandler.MESSAGES.GRID_DATA_LOAD_FAILED
          ),
          type: 'error',
        })
      } finally {
        setLoading(false) // 结束加载
      }
    }

    // 立即执行异步初始化
    initializeColumns()
    initializeData()
  }

  // 创建防抖的数据获取函数，防止频繁API调用
  const debouncedDataFetch = createDebouncedFunction(
    'handleDataFetch',
    async (query: QueryParams) => {
      if (modelApiRef.value && !abortController.value?.signal.aborted) {
        try {
          setLoading(true) // 开始加载
          // 执行数据请求
          await dataHelpers.getDataGridData(
            gridOptionsRef,
            modelApiRef.value,
            query
          )
          // 数据更新后重新同步 footer 配置
          syncFooterToGridOptions()
        } catch (error) {
          // 如果是取消操作，不显示错误
          if (error instanceof Error && error.name === 'AbortError') {
            return
          }

          errorHandler.handleError(error, {
            message: ErrorHandler.getUserFriendlyMessage(
              ErrorHandler.MESSAGES.GRID_DATA_LOAD_FAILED
            ),
            type: 'error',
          })
        } finally {
          setLoading(false) // 结束加载
        }
      }
    },
    300 // 300ms 防抖延迟
  )

  const handleDataFetch = async (query: QueryParams) => {
    // 使用防抖函数处理数据获取
    debouncedDataFetch(query)
  }

  // 添加刷新数据的方法
  const refreshData = async () => {
    if (modelApiRef.value) {
      try {
        setLoading(true) // 开始加载
        // 开始刷新数据
        await dataHelpers.getDataGridData(gridOptionsRef, modelApiRef.value)
        // 数据更新后重新同步 footer 配置
        syncFooterToGridOptions()
        // 数据刷新完成
      } catch (error) {
        errorHandler.handleError(error, {
          message: ErrorHandler.getUserFriendlyMessage(
            ErrorHandler.MESSAGES.GRID_REFRESH_FAILED
          ),
          type: 'error',
        })
      } finally {
        setLoading(false) // 结束加载
      }
    } else {
      errorHandler.handleWarning(
        ErrorHandler.getUserFriendlyMessage(
          ErrorHandler.MESSAGES.MODEL_API_NOT_LOADED
        )
      )
    }
  }

  // 清理函数 - 完善的内存清理机制
  const cleanup = () => {
    try {
      // 1. 取消所有进行中的API请求
      if (abortController.value) {
        abortController.value.abort()
        abortController.value = null
      }

      // 2. 清理所有防抖定时器
      debouncedTimeouts.value.forEach((timeout) => {
        clearTimeout(timeout)
      })
      debouncedTimeouts.value.clear()

      // 4. 清空grid引用
      gridRef.value = undefined

      // 5. 清空ModelApi引用
      modelApiRef.value = null

      // 6. 清空事件处理器
      gridEvents.value = {}
      customEvents.value = {}

      // 7. 执行所有自定义清理任务
      cleanupTasks.value.forEach((task) => {
        try {
          task()
        } catch (error) {
          console.warn('[useDataGrid] 清理任务执行失败:', error)
        }
      })
      cleanupTasks.value = []

      // 8. 重置选择状态
      lastSelectionState = {
        count: 0,
        hasSelection: false,
        selection: [],
      }

      // 9. 智能清理缓存（只有当没有其他实例使用时才清理）
      smartClearCache(moduleModel)
    } catch (error) {
      console.error('[useDataGrid] 清理过程中发生错误:', error)
    }
  }

  // 添加清理任务的方法
  const addCleanupTask = (task: () => void) => {
    cleanupTasks.value.push(task)
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    cleanup()
  })

  return {
    moduleModel,
    gridOptions: gridOptionsRef,
    modelApi: modelApiRef, // 暴露 ModelApi 实例引用
    loading: loadingRef, // 暴露 loading 状态
    // 便捷方法
    on,
    handleDataFetch,
    refreshData,
    // Loading 控制方法
    setLoading,
    // 缓存管理
    clearCache: () => clearCacheForModule(moduleModel),
    // 插件管理器
    getPluginManager: () => pluginManager,
    getColumnHelper: () => pluginManager.getColumnHelper(),
    // 选择相关的方法（仅 checkbox 和 radio）
    getSelection: () => {
      return selectionHelpers.getCheckboxRadioSelection(gridRef)
    },
    getSelectionCount: () => {
      return selectionHelpers.getCheckboxRadioSelectionCount(gridRef)
    },
    hasSelection: () => {
      return selectionHelpers.hasSelection(gridRef)
    },
    // 操作方法
    clearSelection: () => {
      selectionHelpers.clearSelection(gridRef)
      // 触发选择变化事件
      emitSelectionEvents()
      // 额外触发清除事件
      emitCustomEvent('selection-clear', { selection: [] })
    },
    setSelection: (
      rows: unknown | unknown[],
      checked: boolean = true,
      selectionType?: 'checkbox' | 'radio' | 'seq'
    ) => {
      selectionHelpers.setSelection(gridRef, rows, checked, selectionType)
      // 触发选择变化事件
      emitSelectionEvents()
    },
    setAllSelection: (checked: boolean = true) => {
      selectionHelpers.setAllSelection(gridRef, checked)
      // 触发选择变化事件
      emitSelectionEvents()
    },
    // 内部方法，用于设置grid实例引用
    _setGridRef: (ref: VxeGridInstance) => {
      gridRef.value = ref
      // 初始化选择状态
      const selection = selectionHelpers.getCheckboxRadioSelection(gridRef)
      const count = selection.length
      const hasSelection = count > 0
      lastSelectionState = { count, hasSelection, selection }
    },
    // 暴露自定义事件触发器（带状态检查）
    _emitCustomEvent: (eventName: string, payload: unknown) => {
      if (
        eventName === 'has-selection' ||
        eventName === 'selection-change' ||
        eventName === 'selection-count-change'
      ) {
        // 对于选择相关事件，使用状态检查机制
        const currentSelection =
          selectionHelpers.getCheckboxRadioSelection(gridRef)
        const currentCount = currentSelection.length
        const currentHasSelection = currentCount > 0

        if (
          lastSelectionState.count !== currentCount ||
          lastSelectionState.hasSelection !== currentHasSelection
        ) {
          lastSelectionState = {
            count: currentCount,
            hasSelection: currentHasSelection,
            selection: currentSelection,
          }

          if (eventName === 'selection-change') {
            emitCustomEvent('selection-change', {
              selection: currentSelection,
              count: currentCount,
            })
          } else if (eventName === 'has-selection') {
            emitCustomEvent('has-selection', {
              hasSelection: currentHasSelection,
            })
          } else if (eventName === 'selection-count-change') {
            emitCustomEvent('selection-count-change', { count: currentCount })
          }
        }
      } else {
        // 对于其他事件，直接触发
        emitCustomEvent(eventName, payload)
      }
    },
    // 检查是否有指定的自定义事件监听器
    _hasCustomEventListeners: (eventNames: string[]) => {
      return eventNames.some((name) => customEvents.value[name])
    },
    // 内存管理方法
    cleanup,
    addCleanupTask,
    createDebouncedFunction,
  }
}
