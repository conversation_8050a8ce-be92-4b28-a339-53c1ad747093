/**
 * 统一错误处理服务
 * 提供用户友好的错误提示和标准化的错误日志
 */

export interface ErrorConfig {
  message: string
  type: 'error' | 'warning' | 'info'
  showToUser: boolean
  logToConsole: boolean
  duration?: number
}

export class ErrorHandler {
  private static instance: ErrorHandler | null = null
  private toastFunction: ((message: string, type: string, duration?: number) => void) | null = null
  private errorCount: Map<string, number> = new Map()
  private readonly errorThreshold: number = 3

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!this.instance) {
      this.instance = new ErrorHandler()
    }
    return this.instance
  }

  /**
   * 设置Toast通知函数
   * @param toastFn Toast通知函数
   */
  setToastFunction(toastFn: (message: string, type: string, duration?: number) => void) {
    this.toastFunction = toastFn
  }

  /**
   * 处理错误
   * @param error 错误对象或消息
   * @param config 错误配置
   */
  handleError(error: Error | string, config?: Partial<ErrorConfig>) {
    const errorMessage = this.getErrorMessage(error)
    const defaultConfig: ErrorConfig = {
      message: errorMessage,
      type: 'error',
      showToUser: true,
      logToConsole: true,
      duration: 5000,
      ...config
    }

    // 频率控制检查
    if (this.shouldThrottleError(errorMessage)) {
      // 错误发生过于频繁，抑制显示
      if (defaultConfig.logToConsole) {
        console.warn(`[ErrorHandler] 错误被抑制（过于频繁）: ${errorMessage}`)
      }
      return
    }

    // 记录到控制台
    if (defaultConfig.logToConsole) {
      console.error(`[ErrorHandler] ${defaultConfig.message}`, error instanceof Error ? error : undefined)
    }

    // 显示给用户
    if (defaultConfig.showToUser && this.toastFunction) {
      this.toastFunction(defaultConfig.message, defaultConfig.type, defaultConfig.duration)
    }
  }

  /**
   * 处理警告
   * @param message 警告消息
   * @param config 配置
   */
  handleWarning(message: string, config?: Partial<ErrorConfig>) {
    this.handleError(message, { 
      type: 'warning', 
      duration: 3000,
      ...config 
    })
    
    // 在开发环境输出警告到控制台
    if (import.meta.env.DEV) {
      console.warn('[DataGrid] 警告:', message)
    }
  }

  /**
   * 处理信息提示
   * @param message 信息消息
   * @param config 配置
   */
  handleInfo(message: string, config?: Partial<ErrorConfig>) {
    this.handleError(message, { 
      type: 'info', 
      duration: 2000,
      ...config 
    })
  }

  /**
   * 获取错误消息
   * @param error 错误对象或消息
   * @returns 错误消息字符串
   */
  private getErrorMessage(error: Error | string): string {
    if (typeof error === 'string') {
      return error
    }

    if (error instanceof Error) {
      return error.message || '未知错误'
    }

    return '未知错误'
  }

  /**
   * 预定义的错误消息
   */
  static readonly MESSAGES = {
    // 数据网格相关错误
    GRID_INIT_FAILED: '数据网格初始化失败',
    GRID_DATA_LOAD_FAILED: '数据加载失败',
    GRID_REFRESH_FAILED: '数据刷新失败',
    API_NOT_FOUND: '无法找到API接口',
    MODEL_API_NOT_LOADED: '模型API尚未加载完成',
    
    // 搜索相关错误
    SEARCH_FILTER_ADD_FAILED: '添加筛选条件失败',
    SEARCH_FILTER_REMOVE_FAILED: '删除筛选条件失败',
    SEARCH_APPLY_FAILED: '应用搜索条件失败',
    SEARCH_FAVORITE_NOT_FOUND: '收藏项不存在',
    SEARCH_FAVORITE_LOAD_FAILED: '加载收藏失败',
    SEARCH_FAVORITE_SAVE_FAILED: '保存收藏失败',
    
    // 通用错误
    QUERY_PARAMS_NOT_FOUND: '无法找到查询参数',
    INVALID_CONFIGURATION: '配置参数无效',
    OPERATION_CANCELLED: '操作已取消',
    PERMISSION_DENIED: '权限不足',
    NETWORK_ERROR: '网络连接错误',
    SERVER_ERROR: '服务器错误',
    VALIDATION_ERROR: '数据验证失败',
  } as const

  /**
   * 预定义的用户友好错误消息映射
   */
  static readonly USER_FRIENDLY_MESSAGES = {
    [ErrorHandler.MESSAGES.GRID_INIT_FAILED]: '表格初始化失败，请刷新页面重试',
    [ErrorHandler.MESSAGES.GRID_DATA_LOAD_FAILED]: '数据加载失败，请检查网络连接后重试',
    [ErrorHandler.MESSAGES.GRID_REFRESH_FAILED]: '数据刷新失败，请稍后重试',
    [ErrorHandler.MESSAGES.API_NOT_FOUND]: '接口配置错误，请联系管理员',
    [ErrorHandler.MESSAGES.MODEL_API_NOT_LOADED]: '系统正在加载中，请稍候...',
    
    [ErrorHandler.MESSAGES.SEARCH_FILTER_ADD_FAILED]: '添加搜索条件失败，请重试',
    [ErrorHandler.MESSAGES.SEARCH_FILTER_REMOVE_FAILED]: '删除搜索条件失败，请重试',
    [ErrorHandler.MESSAGES.SEARCH_APPLY_FAILED]: '应用搜索条件失败，请检查输入内容',
    [ErrorHandler.MESSAGES.SEARCH_FAVORITE_NOT_FOUND]: '收藏项不存在或已被删除',
    [ErrorHandler.MESSAGES.SEARCH_FAVORITE_LOAD_FAILED]: '加载收藏列表失败',
    [ErrorHandler.MESSAGES.SEARCH_FAVORITE_SAVE_FAILED]: '保存收藏失败，请重试',
    
    [ErrorHandler.MESSAGES.QUERY_PARAMS_NOT_FOUND]: '查询参数配置错误',
    [ErrorHandler.MESSAGES.INVALID_CONFIGURATION]: '配置参数无效，请检查配置',
    [ErrorHandler.MESSAGES.OPERATION_CANCELLED]: '操作已取消',
    [ErrorHandler.MESSAGES.PERMISSION_DENIED]: '您没有权限执行此操作',
    [ErrorHandler.MESSAGES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
    [ErrorHandler.MESSAGES.SERVER_ERROR]: '服务器暂时无法响应，请稍后重试',
    [ErrorHandler.MESSAGES.VALIDATION_ERROR]: '输入数据格式不正确，请检查后重试',
  } as const

  /**
   * 获取用户友好的错误消息
   * @param errorKey 错误键
   * @returns 用户友好的错误消息
   */
  static getUserFriendlyMessage(errorKey: string): string {
    return this.USER_FRIENDLY_MESSAGES[errorKey as keyof typeof this.USER_FRIENDLY_MESSAGES] || errorKey
  }

  /**
   * 检查是否应该抑制错误（频率控制）
   */
  private shouldThrottleError(errorKey: string): boolean {
    const count = this.errorCount.get(errorKey) || 0
    this.errorCount.set(errorKey, count + 1)

    // 如果错误次数超过阈值，开始抑制
    if (count >= this.errorThreshold) {
      return true
    }

    // 5分钟后重置计数器
    setTimeout(() => {
      this.errorCount.delete(errorKey)
    }, 5 * 60 * 1000)

    return false
  }

  /**
   * 重置错误计数器
   */
  resetErrorCounts(): void {
    this.errorCount.clear()
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): { [key: string]: number } {
    return Object.fromEntries(this.errorCount)
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()