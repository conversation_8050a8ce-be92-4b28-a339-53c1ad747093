import type {
  FooterConfig,
  FooterSummaryConfig,
  FooterCalculatorType,
  FooterCalculatorFunction,
} from '../types'

/**
 * 行记录类型
 */
type RowRecord = Record<string, any>

/**
 * 列配置类型
 */
type ColumnConfig = {
  field?: string
  property?: string
  [key: string]: any
}

/**
 * VxeGrid footer方法参数类型
 */
type FooterMethodParams = {
  columns: ColumnConfig[]
  data: RowRecord[]
}

/**
 * 提取数字值，支持字符串转数字
 * @param value 待转换的值
 * @returns 转换后的数字，转换失败返回null
 */
function extractNumber(value: unknown): number | null {
  if (value == null || value === '') return null

  if (typeof value === 'number') {
    return isNaN(value) ? null : value
  }

  if (typeof value === 'string') {
    // 移除常见的非数字字符（如货币符号、逗号等）
    const cleaned = value.replace(/[^\d.-]/g, '')
    const num = parseFloat(cleaned)
    return isNaN(num) ? null : num
  }

  // 尝试转换为数字
  const num = Number(value)
  return isNaN(num) ? null : num
}

/**
 * 获取对象字段值，支持嵌套路径（如 'user.name'）
 * @param obj 目标对象
 * @param path 字段路径，支持点分隔的嵌套路径
 * @returns 字段值
 */
function getFieldValue(obj: Record<string, any>, path: string): unknown {
  if (!path || !obj) return obj

  const keys = path.split('.')
  let result: any = obj

  for (const key of keys) {
    if (result == null) return null
    result = result[key]
  }

  return result
}

/**
 * 内置计算器实现
 * 提供常用的统计计算功能
 */
const builtinCalculators: Record<
  FooterCalculatorType,
  FooterCalculatorFunction
> = {
  /**
   * 求和计算器
   */
  sum: (values: unknown[]) => {
    return values.reduce((sum: number, val) => {
      const num = extractNumber(val)
      return num !== null ? sum + num : sum
    }, 0)
  },

  /**
   * 平均值计算器
   */
  avg: (values: unknown[]) => {
    const nums = values
      .map((val) => extractNumber(val))
      .filter((num): num is number => num !== null)

    return nums.length > 0
      ? nums.reduce((sum, val) => sum + val, 0) / nums.length
      : 0
  },

  /**
   * 最大值计算器
   */
  max: (values: unknown[]) => {
    const nums = values
      .map((val) => extractNumber(val))
      .filter((num): num is number => num !== null)

    return nums.length > 0 ? Math.max(...nums) : null
  },

  /**
   * 最小值计算器
   */
  min: (values: unknown[]) => {
    const nums = values
      .map((val) => extractNumber(val))
      .filter((num): num is number => num !== null)

    return nums.length > 0 ? Math.min(...nums) : null
  },

  /**
   * 计数计算器 - 返回所有值的数量
   */
  count: (values: unknown[]) => {
    return values.length
  },

  /**
   * 非空计数计算器 - 返回非空值的数量
   */
  countNonNull: (values: unknown[]) => {
    return values.filter((val) => val != null && val !== '').length
  },
}

/**
 * 计算单个字段的统计值
 * @param data 数据行数组
 * @param field 字段名
 * @param config 统计配置
 * @returns 计算结果
 */
export function calculateFooterSummary(
  data: RowRecord[],
  field: string,
  config: FooterSummaryConfig
): unknown {
  if (!data || data.length === 0) {
    return config.showEmpty ? config.emptyText || '-' : ''
  }

  try {
    // 提取字段值
    let values: unknown[]

    if (config.condition) {
      // 应用条件过滤
      values = data
        .filter((row) => {
          const fieldValue = getFieldValue(row, field)
          return config.condition!(fieldValue, row)
        })
        .map((row) => getFieldValue(row, field))
    } else {
      values = data.map((row) => getFieldValue(row, field))
    }

    // 执行计算
    let result: unknown
    if (typeof config.calculator === 'string') {
      const calculator = builtinCalculators[config.calculator]
      if (!calculator) {
        throw new Error(`Unknown calculator: ${config.calculator}`)
      }
      result = calculator(values, data)
    } else if (typeof config.calculator === 'function') {
      result = config.calculator(values, data)
    } else {
      // 默认求和
      result = builtinCalculators.sum(values, data)
    }

    return result
  } catch (error) {
    console.error(`[footerHelpers] 计算字段 ${field} 的统计值失败:`, error)
    return config.showEmpty ? config.emptyText || '-' : ''
  }
}

/**
 * 格式化统计值显示
 * @param value 待格式化的值
 * @param config 统计配置
 * @returns 格式化后的字符串
 */
export function formatFooterValue(
  value: unknown,
  config: FooterSummaryConfig
): string {
  if (value == null || value === '') {
    return config.showEmpty ? config.emptyText || '-' : ''
  }

  let formattedValue: string

  // 应用自定义格式化函数
  if (config.formatter) {
    try {
      formattedValue = config.formatter(value)
    } catch (error) {
      console.error('[footerHelpers] 格式化函数执行失败:', error)
      formattedValue = String(value)
    }
  } else {
    // 默认格式化
    if (typeof value === 'number') {
      // 应用精度设置
      if (typeof config.precision === 'number') {
        formattedValue = value.toFixed(config.precision)
      } else {
        formattedValue = String(value)
      }
    } else {
      formattedValue = String(value)
    }
  }

  // 添加前缀和后缀
  if (config.prefix) {
    formattedValue = config.prefix + formattedValue
  }
  if (config.suffix) {
    formattedValue = formattedValue + config.suffix
  }

  return formattedValue
}

/**
 * 创建 VxeGrid 的 footerMethod 函数
 * @param footerConfig footer配置
 * @returns VxeGrid footerMethod函数，如果配置无效则返回null
 */
export function createFooterMethod(footerConfig: FooterConfig) {
  if (!footerConfig.enabled || !footerConfig.summaries) {
    return null
  }

  return ({ columns, data }: FooterMethodParams) => {
    const footerData: string[][] = [[]]

    columns.forEach((column: ColumnConfig, index: number) => {
      const field = column.field || column.property
      let cellValue = ''

      // 跳过序号列、选择列等特殊列
      if (!field || field.startsWith('__')) {
        cellValue = ''
      } else if (footerConfig.summaries![field]) {
        const summaryConfig = footerConfig.summaries![field]

        try {
          if (summaryConfig.calculator || summaryConfig.label) {
            if (summaryConfig.calculator) {
              // 有计算器时进行计算
              const value = calculateFooterSummary(data, field, summaryConfig)
              cellValue = formatFooterValue(value, summaryConfig)
            } else if (summaryConfig.label) {
              // 没有计算器时显示 label
              cellValue = summaryConfig.label
            }
          }
        } catch (error) {
          console.error(
            `[footerHelpers] 处理字段 ${field} 的footer失败:`,
            error
          )
          cellValue = summaryConfig.showEmpty
            ? summaryConfig.emptyText || '-'
            : ''
        }
      }

      footerData[0][index] = cellValue
    })

    return footerData
  }
}

/**
 * 获取 footer 行样式
 * @param footerConfig footer配置
 * @returns 样式对象
 */
export function getFooterRowStyle(
  footerConfig: FooterConfig
): Record<string, string | number> {
  const defaultStyle = {
    backgroundColor: '#fafafa',
    borderTop: '2px solid #e8e8e8',
    fontWeight: '500',
  } as const

  if (footerConfig.background) {
    return {
      ...defaultStyle,
      backgroundColor: footerConfig.background,
      ...footerConfig.style,
    }
  }

  return {
    ...defaultStyle,
    ...footerConfig.style,
  }
}

/**
 * 获取 footer 单元格样式
 * @param footerConfig footer配置
 * @param field 字段名
 * @param columnIndex 列索引
 * @returns 样式对象
 */
export function getFooterCellStyle(
  footerConfig: FooterConfig,
  field: string,
  columnIndex: number
): Record<string, string | number> {
  const defaultStyle: Record<string, string | number> = {
    color: '#333',
  }

  // 第一列加粗
  if (columnIndex === 0) {
    defaultStyle.fontWeight = 'bold'
  }

  // 应用字段特定样式
  const summaryConfig = footerConfig.summaries?.[field]
  if (summaryConfig?.style) {
    return {
      ...defaultStyle,
      ...(summaryConfig.style as Record<string, string | number>),
    }
  }

  return defaultStyle
}
