import type { Ref } from 'vue'
import type { VxeGridInstance } from 'vxe-table'

/**
 * 选择类型枚举
 */
type SelectionType = 'checkbox' | 'radio' | 'seq'

/**
 * 行记录类型
 */
type RowRecord = Record<string, any>

/**
 * 选择相关的辅助方法
 * 提供统一的选择操作接口，支持checkbox、radio和序号三种选择模式
 */
export const selectionHelpers = {
  /**
   * 获取checkbox和radio选中的行数据（不包括序号选择）
   * @param gridRef vxe-grid实例引用
   * @returns 选中的行数据数组
   */
  getCheckboxRadioSelection(gridRef: Ref<VxeGridInstance | undefined>): RowRecord[] {
    if (!gridRef.value) return []
    
    try {
      // 尝试获取checkbox选中的行
      const checkboxRecords = gridRef.value.getCheckboxRecords()
      if (checkboxRecords && checkboxRecords.length > 0) {
        return checkboxRecords
      }
      
      // 尝试获取radio选中的行
      const radioRecord = gridRef.value.getRadioRecord()
      if (radioRecord) {
        return [radioRecord]
      }
    } catch (error) {
      console.warn('[selectionHelpers] 获取选中行失败:', error)
    }
    
    return []
  },

  /**
   * 获取所有选中的行数据（包括序号选择）
   * 自动判断是checkbox、radio还是序号模式
   * @param gridRef vxe-grid实例引用
   * @returns 选中的行数据数组
   */
  getSelection(gridRef: Ref<VxeGridInstance | undefined>): RowRecord[] {
    if (!gridRef.value) return []
    
    // 先获取checkbox和radio选中的行
    const checkboxRadioSelection = selectionHelpers.getCheckboxRadioSelection(gridRef)
    if (checkboxRadioSelection.length > 0) {
      return checkboxRadioSelection
    }
    
    try {
      // 如果没有checkbox和radio选中，尝试获取当前行（序号选择模式）
      const currentRecord = gridRef.value.getCurrentRecord()
      if (currentRecord) {
        return [currentRecord]
      }
    } catch (error) {
      console.warn('[selectionHelpers] 获取当前行失败:', error)
    }
    
    return []
  },

  /**
   * 清除所有选中状态
   * 自动判断是checkbox还是radio模式并清除相应的选中状态
   * @param gridRef vxe-grid实例引用
   */
  clearSelection(gridRef: Ref<VxeGridInstance | undefined>): void {
    if (!gridRef.value) return
    
    try {
      // 清除checkbox选中状态
      gridRef.value.clearCheckboxRow()
      
      // 清除radio选中状态（通过设置为null）
      gridRef.value.setRadioRow(null)
      
      // 清除当前行选中状态
      gridRef.value.clearCurrentRow()
    } catch (error) {
      console.warn('[selectionHelpers] 清除选中状态失败:', error)
    }
  },

  /**
   * 设置行的选中状态
   * 根据配置自动判断使用checkbox还是radio
   * @param gridRef vxe-grid实例引用
   * @param rows 要设置的行数据，可以是单行或多行
   * @param checked 是否选中
   * @param selectionType 选择类型，如果不提供则自动检测
   */
  setSelection(
    gridRef: Ref<VxeGridInstance | undefined>, 
    rows: RowRecord | RowRecord[], 
    checked: boolean = true,
    selectionType?: SelectionType
  ): void {
    if (!gridRef.value) return
    
    const rowsArray = Array.isArray(rows) ? rows : [rows]
    
    if (!selectionType) {
      // 自动检测选择类型
      selectionType = this._detectSelectionType(gridRef.value)
    }
    
    try {
      switch (selectionType) {
        case 'checkbox':
          gridRef.value.setCheckboxRow(rowsArray, checked)
          break
        case 'radio':
          if (checked && rowsArray.length > 0) {
            gridRef.value.setRadioRow(rowsArray[0])
          } else {
            gridRef.value.setRadioRow(null)
          }
          break
        case 'seq':
          if (checked && rowsArray.length > 0) {
            gridRef.value.setCurrentRow(rowsArray[0])
          } else {
            gridRef.value.clearCurrentRow()
          }
          break
      }
    } catch (error) {
      console.warn('[selectionHelpers] 设置选中状态失败:', error)
    }
  },

  /**
   * 全选/取消全选
   * 仅适用于checkbox模式
   * @param gridRef vxe-grid实例引用
   * @param checked 是否全选
   */
  setAllSelection(gridRef: Ref<VxeGridInstance | undefined>, checked: boolean = true): void {
    if (!gridRef.value) return
    
    try {
      gridRef.value.setAllCheckboxRow(checked)
    } catch (error) {
      console.warn('[selectionHelpers] 设置全选状态失败:', error)
    }
  },

  /**
   * 获取checkbox和radio选中行的数量（不包括序号选择）
   * @param gridRef vxe-grid实例引用
   * @returns 选中行的数量
   */
  getCheckboxRadioSelectionCount(gridRef: Ref<VxeGridInstance | undefined>): number {
    return selectionHelpers.getCheckboxRadioSelection(gridRef).length
  },

  /**
   * 获取选中行的数量（包括当前行选中）
   * @param gridRef vxe-grid实例引用
   * @returns 选中行的数量
   */
  getSelectionCount(gridRef: Ref<VxeGridInstance | undefined>): number {
    return selectionHelpers.getSelection(gridRef).length
  },

  /**
   * 检查是否有checkbox或radio选中的行（不包括序号选择）
   * @param gridRef vxe-grid实例引用
   * @returns 是否有选中的行
   */
  hasSelection(gridRef: Ref<VxeGridInstance | undefined>): boolean {
    return selectionHelpers.getCheckboxRadioSelectionCount(gridRef) > 0
  },

  /**
   * 检查是否有任何类型的选中行（包括序号选择）
   * @param gridRef vxe-grid实例引用
   * @returns 是否有选中的行
   */
  hasAnySelection(gridRef: Ref<VxeGridInstance | undefined>): boolean {
    return selectionHelpers.getSelectionCount(gridRef) > 0
  },

  /**
   * 自动检测选择类型
   * @param gridInstance vxe-grid实例
   * @returns 检测到的选择类型
   * @private
   */
  _detectSelectionType(gridInstance: VxeGridInstance): SelectionType {
    try {
      // 尝试获取checkbox选中记录来判断是否启用了checkbox
      const checkboxRecords = gridInstance.getCheckboxRecords()
      if (checkboxRecords !== null) {
        return 'checkbox'
      }

      // 尝试获取radio记录来判断是否启用了radio
      const radioRecord = gridInstance.getRadioRecord()
      if (radioRecord !== undefined) {
        return 'radio'
      }

      // 默认使用序号选择
      return 'seq'
    } catch {
      // 如果都失败了，默认使用seq
      return 'seq'
    }
  }
}