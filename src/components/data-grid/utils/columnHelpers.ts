import { computed, type Ref } from 'vue'
import type { GridOptions } from '../types'
import type { ModelApi } from '@/api/apiService'
import { mergeHelpers } from './mergeHelpers'

/**
 * 元数据缓存，避免重复处理相同的元数据
 */
const metadataCache = new Map<
  string,
  {
    baseColumnMap: Map<string, any>
    timestamp: number
  }
>()

/**
 * 缓存过期时间（5分钟）
 */
const CACHE_EXPIRE_TIME = 5 * 60 * 1000

/**
 * 字段信息接口
 */
interface FieldInfo {
  name: string
  type:
    | 'string'
    | 'integer'
    | 'float'
    | 'boolean'
    | 'datetime'
    | 'enum'
    | 'relation'
  comment?: string
  nullable?: boolean
  enum_info?: {
    enum_values: Record<string, string>
  }
  relation_info?: {
    direction: string
  }
  operators?: string[]
}

/**
 * 列配置接口
 */
interface ColumnConfig {
  field?: string
  title?: string
  width?: number
  align?: 'left' | 'center' | 'right'
  formatter?: (params: { cellValue: any }) => string
  required?: boolean
  dataType?: string
  operators?: string[]
  enumInfo?: any
  isEnum?: boolean
  relationInfo?: any
  isRelation?: boolean
  [key: string]: any
}

/**
 * 根据字段信息增强列配置
 * @param columnConfig 现有的列配置
 * @param fieldInfo 从API获取的字段信息
 * @returns 增强后的列配置
 */
function enhanceColumn(
  columnConfig: ColumnConfig,
  fieldInfo: FieldInfo
): ColumnConfig {
  // 如果没有字段信息，直接返回原始配置
  if (!fieldInfo) {
    return columnConfig
  }

  const enhanced: ColumnConfig = { ...columnConfig }

  // 基础信息增强
  if (fieldInfo.comment && !enhanced.title) {
    enhanced.title = fieldInfo.comment
  }

  if (fieldInfo.nullable === false && !enhanced.required) {
    enhanced.required = true
  }

  // 添加数据类型和操作符信息
  enhanced.dataType = fieldInfo.type
  if (fieldInfo.operators) {
    enhanced.operators = fieldInfo.operators
  }

  // 根据字段类型进行增强
  switch (fieldInfo.type) {
    case 'string':
      if (!enhanced.width) {
        enhanced.width = 150
      }
      break

    case 'integer':
      if (!enhanced.align) enhanced.align = 'right'
      if (!enhanced.width) enhanced.width = 100
      break

    case 'float': {
      // 智能识别字段用途
      const fieldName = fieldInfo.name.toLowerCase()
      let formatType: 'decimal' | 'percentage' | 'currency' = 'decimal'

      if (
        fieldName.includes('rate') ||
        fieldName.includes('percent') ||
        fieldName.includes('ratio')
      ) {
        formatType = 'percentage'
      } else if (
        fieldName.includes('price') ||
        fieldName.includes('amount') ||
        fieldName.includes('cost') ||
        fieldName.includes('fee')
      ) {
        formatType = 'currency'
      }

      if (!enhanced.formatter) {
        switch (formatType) {
          case 'percentage':
            enhanced.formatter = ({ cellValue }: any) => {
              if (cellValue == null) return ''
              const numValue = Number(cellValue)
              if (isNaN(numValue)) return cellValue

              // 智能判断是0-1小数还是0-100百分比
              if (numValue <= 1) {
                return `${(numValue * 100).toFixed(1)}%`
              } else {
                return `${numValue.toFixed(1)}%`
              }
            }
            break
          case 'currency':
            enhanced.formatter = ({ cellValue }: any) => {
              if (cellValue == null) return ''
              const numValue = Number(cellValue)
              if (isNaN(numValue)) return cellValue
              return `¥${numValue.toFixed(2)}`
            }
            break
          default:
            enhanced.formatter = ({ cellValue }: any) => {
              if (cellValue == null) return ''
              const numValue = Number(cellValue)
              if (isNaN(numValue)) return cellValue
              return numValue.toFixed(2)
            }
        }
      }

      if (!enhanced.align) enhanced.align = 'right'
      if (!enhanced.width) enhanced.width = 100
      break
    }

    case 'boolean':
      if (!enhanced.formatter) {
        enhanced.formatter = ({ cellValue }: any) => (cellValue ? '是' : '否')
      }
      if (!enhanced.width) enhanced.width = 80
      if (!enhanced.align) enhanced.align = 'center'
      break

    case 'datetime':
      if (!enhanced.formatter) {
        enhanced.formatter = ({ cellValue }: any) => {
          if (!cellValue) return ''
          const date = new Date(cellValue)
          return date.toLocaleString('zh-CN')
        }
      }
      if (!enhanced.width) enhanced.width = 160
      break

    case 'enum':
      if (fieldInfo.enum_info) {
        enhanced.enumInfo = fieldInfo.enum_info
        enhanced.isEnum = true

        if (!enhanced.formatter) {
          enhanced.formatter = ({ cellValue }: any) => {
            if (!cellValue) return ''
            return fieldInfo.enum_info.enum_values[cellValue] || cellValue
          }
        }
        if (!enhanced.width) enhanced.width = 120
      }
      break

    case 'relation':
      if (fieldInfo.relation_info) {
        enhanced.relationInfo = fieldInfo.relation_info
        enhanced.isRelation = true

        // 只有在没有formatter时才添加关系格式化
        if (!enhanced.formatter) {
          const direction = fieldInfo.relation_info.direction

          if (direction === 'RelationshipDirection.ONETOMANY') {
            // 一对多关系：显示关联项目数量
            enhanced.formatter = ({ cellValue }: any) => {
              if (!cellValue) return '--'
              if (Array.isArray(cellValue)) {
                const count = cellValue.length
                return count > 0 ? `${count} 项` : '--'
              }
              return '--'
            }
          } else if (direction === 'RelationshipDirection.MANYTOONE') {
            // 多对一关系：显示关联对象的名称
            enhanced.formatter = ({ cellValue }: any) => {
              if (!cellValue) return '-'
              if (typeof cellValue === 'object') {
                // 优先显示 name 或以 _name 结尾的字段，然后 title，最后 code
                const displayValue = (() => {
                  if (cellValue.name) return cellValue.name

                  // 查找以 _name 结尾的字段
                  const nameField = Object.keys(cellValue).find((key) =>
                    key.endsWith('_name')
                  )
                  if (nameField && cellValue[nameField]) return cellValue[nameField]

                  // 回退到其他字段
                  return cellValue.title || cellValue.code || cellValue.id || '-'
                })()
                return displayValue
              }
              return cellValue
            }
          } else {
            // 其他关系类型的默认处理
            enhanced.formatter = ({ cellValue }: any) => {
              if (!cellValue) return '-'
              if (Array.isArray(cellValue)) {
                return `${cellValue.length}项`
              }
              if (typeof cellValue === 'object') {
                return (
                  cellValue.name ||
                  cellValue.title ||
                  cellValue.code ||
                  cellValue.id ||
                  '-'
                )
              }
              return cellValue
            }
          }
        }

        // 设置关系字段的默认宽度
        if (!enhanced.width) {
          if (
            fieldInfo.relation_info.direction === 'RelationshipDirection.ONETOMANY'
          ) {
            enhanced.width = 100 // 一对多显示数量，宽度较小
          } else {
            enhanced.width = 150 // 多对一显示名称，宽度较大
          }
        }

        // 设置对齐方式
        if (!enhanced.align) {
          if (
            fieldInfo.relation_info.direction === 'RelationshipDirection.ONETOMANY'
          ) {
            enhanced.align = 'center' // 数量居中显示
          } else {
            enhanced.align = 'left' // 名称左对齐
          }
        }
      }
      break
  }

  return enhanced
}

/**
 * 根据字段信息构建基础列配置
 * @param field 字段信息
 * @returns 基础列配置
 */
function buildColumn(field: FieldInfo): ColumnConfig {
  const columnInfo: ColumnConfig = {
    field: field.name,
    title: field.comment || field.name,
    dataType: field.type,
    operators: field.operators,
  }
  return enhanceColumn(columnInfo, field)
}

/**
 * 从元数据构建状态映射
 * @param enumInfo 枚举信息
 * @param customStatusMap 用户自定义的状态映射（优先级更高）
 * @returns 状态映射对象
 */
export function buildStatusMapFromMetadata(
  enumInfo?: { enum_values: Record<string, string> },
  customStatusMap?: Record<
    string,
    { text: string; type?: string; icon?: string; progress?: number }
  >
): Record<string, { text: string; type?: string; icon?: string; progress?: number }> {
  if (!enumInfo?.enum_values) {
    return customStatusMap || {}
  }

  const metadataStatusMap: Record<
    string,
    { text: string; key?: string; type?: string; icon?: string; progress?: number }
  > = {}

  // 将枚举值转换为状态映射
  Object.entries(enumInfo.enum_values).forEach(([key, value]) => {
    metadataStatusMap[value] = {
      text: value,
      key: key,
      type: 'default', // 默认类型，可以根据需要自定义
    }
  })

  // 合并用户自定义的状态映射（用户配置优先）
  return { ...metadataStatusMap, ...(customStatusMap || {}) }
}

/**
 * 清理过期的元数据缓存
 */
function cleanExpiredCache() {
  const now = Date.now()
  metadataCache.forEach((value, key) => {
    if (now - value.timestamp > CACHE_EXPIRE_TIME) {
      metadataCache.delete(key)
    }
  })
}

/**
 * 根据元数据和自定义列配置构建数据网格的列配置
 * @param gridOptions - 数据网格的配置选项
 * @param modelApi - 模型 API 实例，用于获取元数据
 */
async function buildDataGridColumns(
  gridOptions: Ref<GridOptions>,
  modelApi: ModelApi
) {
  // 清理过期缓存
  cleanExpiredCache()

  // 生成缓存键
  const cacheKey = `${modelApi.constructor.name}_${modelApi.getModelName?.() || 'unknown'}`

  let baseColumnMap: Map<string, any>

  // 检查缓存
  const cached = metadataCache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_EXPIRE_TIME) {
    baseColumnMap = cached.baseColumnMap
  } else {
    // 获取元数据并构建基础列配置
    const metadataData: any = await modelApi.getMetadata()

    baseColumnMap = new Map()
    metadataData.fields.forEach((field: any) => {
      if (field.name === 'id' || field.name.endsWith('_id')) {
        return
      }
      const baseColumn: any = buildColumn(field)
      baseColumnMap.set(field.name, baseColumn)
    })

    // 缓存结果
    metadataCache.set(cacheKey, {
      baseColumnMap,
      timestamp: Date.now(),
    })
  }

  // 使用计算属性来构建最终的列配置 - 具有响应式特性
  // 使用 shallowRef 来避免深度响应式，提高性能
  const adaptedColumns = computed(() => {
    // 每次计算时获取最新的自定义列配置
    const customColumns = gridOptions.value.columns || []

    // 性能优化：如果没有自定义列配置，直接返回缓存的基础列
    if (customColumns.length === 0) {
      return Array.from(baseColumnMap.values())
    }

    // 性能优化：创建一个副本来避免修改原始 Map
    const remainingColumns = new Map(baseColumnMap)
    const orderedAndMergedColumns: any[] = []

    // 按照 customColumns 的顺序构建列配置
    for (const customColumn of customColumns) {
      if (customColumn.field && remainingColumns.has(customColumn.field)) {
        const metaCol = remainingColumns.get(customColumn.field)!
        // 从副本中删除已处理的列
        remainingColumns.delete(customColumn.field)

        // 深度合并元数据列和自定义列配置
        orderedAndMergedColumns.push(
          mergeHelpers.deepMerge(metaCol, customColumn)
        )
      } else {
        // 这是仅在 customColumns 中定义的新列
        orderedAndMergedColumns.push(customColumn)
      }
    }

    // 添加剩余的未处理的元数据列
    const remainingMetaColumns = Array.from(remainingColumns.values())
    return [...orderedAndMergedColumns, ...remainingMetaColumns]
  })

  if (
    gridOptions.value.enableSelection === true ||
    gridOptions.value.enableSelection === 'checkbox'
  ) {
    adaptedColumns.value.unshift({
      type: 'checkbox',
      resizable: true,
      width: 50,
      align: 'center',
      fixed: 'left',
    })
  } else if (gridOptions.value.enableSelection === 'radio') {
    adaptedColumns.value.unshift({
      type: 'radio',
      resizable: true,
      width: 50,
      align: 'center',
      fixed: 'left',
    })
  } else if (gridOptions.value.enableSelection === 'seq') {
    adaptedColumns.value.unshift({
      type: 'seq',
      resizable: true,
      width: 50,
      align: 'center',
      fixed: 'left',
    })
  }

  gridOptions.value.columns = adaptedColumns.value
}

export const columnHelpers = {
  buildDataGridColumns,
}
