/**
 * 数组合并策略类型
 */
type ArrayMergeStrategy = 'union' | 'intersection' | 'concat' | 'replace'

/**
 * 判断是否为纯对象（Plain Object）
 * @param obj 待检查的对象
 * @returns 是否为纯对象
 */
function isPlainObject(obj: unknown): obj is Record<string, any> {
  if (typeof obj !== 'object' || obj === null) return false
  if (Object.prototype.toString.call(obj) !== '[object Object]') return false

  // 检查原型链
  if (Object.getPrototypeOf(obj) === null) return true

  let proto = obj
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto)
  }

  return Object.getPrototypeOf(obj) === proto
}

/**
 * 深度比较两个值是否相等
 * @param a 第一个值
 * @param b 第二个值
 * @returns 是否相等
 */
function isEqual(a: any, b: any): boolean {
  if (a === b) return true
  if (a == null || b == null) return a === b
  if (typeof a !== typeof b) return false

  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false
    return a.every((item, index) => isEqual(item, b[index]))
  }

  if (isPlainObject(a) && isPlainObject(b)) {
    const keysA = Object.keys(a)
    const keysB = Object.keys(b)
    if (keysA.length !== keysB.length) return false
    return keysA.every((key) => isEqual(a[key], b[key]))
  }

  return false
}

/**
 * 数组并集操作
 * @param arr1 第一个数组
 * @param arr2 第二个数组
 * @returns 并集结果
 */
function arrayUnion<T>(arr1: T[], arr2: T[]): T[] {
  const result = [...arr1]
  for (const item of arr2) {
    if (!result.some((existing) => isEqual(existing, item))) {
      result.push(item)
    }
  }
  return result
}

/**
 * 数组交集操作
 * @param arr1 第一个数组
 * @param arr2 第二个数组
 * @returns 交集结果
 */
function arrayIntersection<T>(arr1: T[], arr2: T[]): T[] {
  return arr1.filter((item) => arr2.some((other) => isEqual(item, other)))
}

/**
 * 递归合并两个对象
 *
 * @param source 源对象
 * @param target 目标对象，合并结果存放于此
 * @param mergeArrays 数组合并策略，默认为 "replace"
 *   - "union": 对数组执行并集操作
 *   - "intersection": 对数组执行交集操作
 *   - "concat": 连接数组
 *   - "replace": 用目标数组替换源数组
 * @returns 合并后的对象
 */
function deepMerge<
  T extends object | null | undefined,
  U extends object | null | undefined,
>(source: T, target: U, mergeArrays: ArrayMergeStrategy = 'replace'): T & U {
  if (!target) {
    return source as T & U
  }
  if (!source) {
    return target as T & U
  }

  try {
    const result = { ...source } as any

    for (const key in target) {
      if (target.hasOwnProperty(key)) {
        const sourceValue = (source as any)[key]
        const targetValue = target[key]

        if (Array.isArray(sourceValue) && Array.isArray(targetValue)) {
          // 处理数组合并
          switch (mergeArrays) {
            case 'union':
              result[key] = arrayUnion(sourceValue, targetValue)
              break
            case 'intersection':
              result[key] = arrayIntersection(sourceValue, targetValue)
              break
            case 'concat':
              result[key] = sourceValue.concat(targetValue)
              break
            case 'replace':
              result[key] = targetValue
              break
            default:
              throw new Error(`未知的数组合并策略: ${mergeArrays}`)
          }
        } else if (isPlainObject(sourceValue) && isPlainObject(targetValue)) {
          // 递归合并纯对象
          result[key] = deepMerge(
            sourceValue,
            targetValue as object,
            mergeArrays
          )
        } else {
          // 其他情况直接使用目标值
          result[key] = targetValue
        }
      }
    }

    return result as T & U
  } catch (error) {
    console.error('[mergeHelpers] 深度合并失败:', error)
    // 失败时返回目标对象
    return target as T & U
  }
}

/**
 * 浅合并两个对象（性能优化版本）
 * @param source 源对象
 * @param target 目标对象
 * @returns 合并后的对象
 */
function shallowMerge<T extends object, U extends object>(
  source: T,
  target: U
): T & U {
  return { ...source, ...target } as T & U
}

/**
 * 克隆对象（深拷贝）
 * @param obj 待克隆的对象
 * @returns 克隆后的对象
 */
function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as T
  }

  if (isPlainObject(obj)) {
    const cloned = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        ;(cloned as any)[key] = deepClone((obj as any)[key])
      }
    }
    return cloned
  }

  return obj
}

/**
 * 合并多个对象
 * @param objects 要合并的对象数组
 * @param mergeArrays 数组合并策略
 * @returns 合并后的对象
 */
function mergeMultiple<T extends object>(
  objects: T[],
  mergeArrays: ArrayMergeStrategy = 'replace'
): T {
  if (objects.length === 0) return {} as T
  if (objects.length === 1) return objects[0]

  return objects.reduce((acc, obj) => deepMerge(acc, obj, mergeArrays)) as T
}

export const mergeHelpers = {
  deepMerge,
  shallowMerge,
  deepClone,
  mergeMultiple,
  isPlainObject,
  isEqual,
  arrayUnion,
  arrayIntersection,
}
