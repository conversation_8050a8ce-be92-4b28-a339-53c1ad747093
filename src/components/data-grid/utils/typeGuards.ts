/**
 * Data-Grid 运行时类型检查工具
 * 提供类型验证和转换功能，增强类型安全性
 */

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

/**
 * 基础类型守卫函数
 */

/**
 * 检查值是否为字符串
 * @param value 待检查的值
 * @returns 是否为字符串
 */
export const isString = (value: unknown): value is string => {
  return typeof value === 'string'
}

/**
 * 检查值是否为有效数字（不包括NaN）
 * @param value 待检查的值
 * @returns 是否为有效数字
 */
export const isNumber = (value: unknown): value is number => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value)
}

/**
 * 检查值是否为布尔值
 * @param value 待检查的值
 * @returns 是否为布尔值
 */
export const isBoolean = (value: unknown): value is boolean => {
  return typeof value === 'boolean'
}

/**
 * 检查值是否为对象（不包括null和数组）
 * @param value 待检查的值
 * @returns 是否为对象
 */
export const isObject = (value: unknown): value is Record<string, unknown> => {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

/**
 * 检查值是否为数组
 * @param value 待检查的值
 * @returns 是否为数组
 */
export const isArray = (value: unknown): value is unknown[] => {
  return Array.isArray(value)
}

/**
 * 检查值是否为函数
 * @param value 待检查的值
 * @returns 是否为函数
 */
export const isFunction = (value: unknown): value is Function => {
  return typeof value === 'function'
}

/**
 * 检查值是否为null或undefined
 * @param value 待检查的值
 * @returns 是否为null或undefined
 */
export const isNullish = (value: unknown): value is null | undefined => {
  return value == null
}

/**
 * 检查值是否为非空字符串
 * @param value 待检查的值
 * @returns 是否为非空字符串
 */
export const isNonEmptyString = (value: unknown): value is string => {
  return isString(value) && value.trim().length > 0
}

/**
 * 检查值是否为正数
 * @param value 待检查的值
 * @returns 是否为正数
 */
export const isPositiveNumber = (value: unknown): value is number => {
  return isNumber(value) && value > 0
}

/**
 * 检查值是否为非负数
 * @param value 待检查的值
 * @returns 是否为非负数
 */
export const isNonNegativeNumber = (value: unknown): value is number => {
  return isNumber(value) && value >= 0
}

/**
 * 检查值是否为有效的CSS尺寸值
 * @param value 待检查的值
 * @returns 是否为有效的CSS尺寸值
 */
export const isCSSSize = (value: unknown): value is string | number => {
  if (isNumber(value)) return value >= 0
  if (isString(value)) {
    // 支持常见的CSS单位
    return /^\d+(\.\d+)?(px|%|em|rem|vh|vw|vmin|vmax|ex|ch|fr|auto)$/.test(value.trim())
  }
  return false
}

/**
 * 复合类型检查
 */

/**
 * 检查值是否为有效的Grid配置
 * @param value 待检查的值
 * @returns 是否为有效的Grid配置
 */
export const isValidGridOptions = (
  value: unknown
): value is Record<string, unknown> => {
  if (!isObject(value)) return false

  // 检查可选属性的类型
  if ('columns' in value && value.columns !== undefined && !isArray(value.columns)) {
    return false
  }
  
  if ('data' in value && value.data !== undefined && !isArray(value.data)) {
    return false
  }
  
  if ('loading' in value && value.loading !== undefined && !isBoolean(value.loading)) {
    return false
  }

  return true
}

/**
 * 检查值是否为有效的列配置
 * @param value 待检查的值
 * @returns 是否为有效的列配置
 */
export const isValidColumnConfig = (
  value: unknown
): value is Record<string, unknown> => {
  if (!isObject(value)) return false

  // 检查必需的field属性
  const hasValidField = 'field' in value && isNonEmptyString(value.field)
  if (!hasValidField) return false

  // 检查可选属性
  if ('title' in value && value.title !== undefined && !isString(value.title)) {
    return false
  }

  if ('width' in value && value.width !== undefined && !isCSSSize(value.width)) {
    return false
  }

  if ('align' in value && value.align !== undefined) {
    const validAligns = ['left', 'center', 'right']
    if (!isString(value.align) || !validAligns.includes(value.align)) {
      return false
    }
  }

  return true
}

/**
 * 检查值是否为有效的插槽配置
 * @param value 待检查的值
 * @returns 是否为有效的插槽配置
 */
export const isValidSlotConfig = (
  value: unknown
): value is Record<string, unknown> => {
  if (!isObject(value)) return false

  // 至少需要有一个插槽配置属性
  const hasType = 'type' in value && isString(value.type)
  const hasComponent = 'component' in value && (isString(value.component) || isFunction(value.component))
  const hasRender = 'render' in value && isFunction(value.render)

  return hasType || hasComponent || hasRender
}

/**
 * 检查值是否为有效的事件处理器
 * @param value 待检查的值
 * @returns 是否为有效的事件处理器
 */
export const isValidEventHandler = (value: unknown): value is Function => {
  return isFunction(value)
}

/**
 * 检查值是否为有效的选择类型
 * @param value 待检查的值
 * @returns 是否为有效的选择类型
 */
export const isValidSelectionType = (value: unknown): value is 'checkbox' | 'radio' | 'seq' => {
  return isString(value) && ['checkbox', 'radio', 'seq'].includes(value)
}

/**
 * 类型转换工具
 */

/**
 * 安全的JSON解析
 * @param jsonString JSON字符串
 * @param fallback 解析失败时的默认值
 * @returns 解析结果或默认值
 */
export const safeParseJSON = <T = unknown>(
  jsonString: string,
  fallback: T
): T => {
  if (!isString(jsonString) || jsonString.trim() === '') {
    return fallback
  }

  try {
    return JSON.parse(jsonString) as T
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('[typeGuards] JSON解析失败:', error)
    }
    return fallback
  }
}

/**
 * 安全的JSON字符串化
 * @param value 待转换的值
 * @param pretty 是否格式化输出
 * @returns JSON字符串
 */
export const safeStringify = (value: unknown, pretty = false): string => {
  try {
    return JSON.stringify(value, null, pretty ? 2 : 0)
  } catch (error) {
    if (import.meta.env.DEV) {
      console.warn('[typeGuards] JSON字符串化失败:', error)
    }
    return String(value)
  }
}

/**
 * 安全的数字转换
 * @param value 待转换的值
 * @param fallback 转换失败时的默认值
 * @returns 转换结果或默认值
 */
export const safeToNumber = (value: unknown, fallback = 0): number => {
  if (isNumber(value)) return value
  
  if (isString(value)) {
    const parsed = parseFloat(value)
    return isNumber(parsed) ? parsed : fallback
  }
  
  return fallback
}

/**
 * 安全的字符串转换
 * @param value 待转换的值
 * @param fallback 转换失败时的默认值
 * @returns 转换结果或默认值
 */
export const safeToString = (value: unknown, fallback = ''): string => {
  if (isString(value)) return value
  if (isNullish(value)) return fallback
  
  try {
    return String(value)
  } catch {
    return fallback
  }
}

// 类型断言工具
export const assertIsString = (
  value: unknown,
  message = 'Expected string'
): asserts value is string => {
  if (!isString(value)) {
    throw new TypeError(message)
  }
}

export const assertIsNumber = (
  value: unknown,
  message = 'Expected number'
): asserts value is number => {
  if (!isNumber(value)) {
    throw new TypeError(message)
  }
}

export const assertIsObject = (
  value: unknown,
  message = 'Expected object'
): asserts value is Record<string, unknown> => {
  if (!isObject(value)) {
    throw new TypeError(message)
  }
}

/**
 * 深度类型验证
 */

export const validateGridOptions = (options: unknown): ValidationResult => {
  const errors: string[] = []

  if (!isObject(options)) {
    errors.push('Grid options must be an object')
    return { isValid: false, errors }
  }

  // 验证列配置
  if ('columns' in options && options.columns !== undefined) {
    if (!isArray(options.columns)) {
      errors.push('Columns must be an array')
    } else {
      options.columns.forEach((col, index) => {
        if (!isValidColumnConfig(col)) {
          errors.push(`Invalid column configuration at index ${index}`)
        }
      })
    }
  }

  // 验证数据
  if ('data' in options && options.data !== undefined) {
    if (!isArray(options.data)) {
      errors.push('Data must be an array')
    }
  }

  // 验证加载状态
  if ('loading' in options && options.loading !== undefined) {
    if (!isBoolean(options.loading)) {
      errors.push('Loading must be a boolean')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

export const validateColumnConfig = (column: unknown): ValidationResult => {
  const errors: string[] = []

  if (!isObject(column)) {
    errors.push('Column must be an object')
    return { isValid: false, errors }
  }

  // 必需字段检查
  if (!('field' in column) || !isString(column.field)) {
    errors.push('Column must have a valid field property')
  }

  if ('title' in column && !isString(column.title)) {
    errors.push('Column title must be a string')
  }

  if ('type' in column && !isString(column.type)) {
    errors.push('Column type must be a string')
  }

  if (
    'width' in column &&
    column.width !== undefined &&
    !isNumber(column.width) &&
    !isString(column.width)
  ) {
    errors.push('Column width must be a number or string')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// 类型安全的工具函数
export const getTypedProperty = <T>(
  obj: Record<string, unknown>,
  key: string,
  fallback: T,
  typeGuard: (value: unknown) => value is T
): T => {
  const value = obj[key]
  return typeGuard(value) ? value : fallback
}

// 运行时类型检查装饰器（用于开发环境）
export const withTypeCheck = <T extends any[], R>(
  fn: (...args: T) => R,
  validators: Array<(arg: unknown) => boolean>
) => {
  return (...args: T): R => {
    if (import.meta.env.DEV) {
      args.forEach((arg, index) => {
        const validator = validators[index]
        if (validator && !validator(arg)) {
          console.warn(
            `Type check failed for argument ${index} in function ${fn.name}`
          )
        }
      })
    }
    return fn(...args)
  }
}

/**
 * 导出类型检查器集合
 */
export const TypeCheckers = {
  // 基础类型检查
  isString,
  isNumber,
  isBoolean,
  isObject,
  isArray,
  isFunction,
  isNullish,
  
  // 扩展类型检查
  isNonEmptyString,
  isPositiveNumber,
  isNonNegativeNumber,
  isCSSSize,
  
  // 复合类型检查
  isValidGridOptions,
  isValidColumnConfig,
  isValidSlotConfig,
  isValidEventHandler,
  isValidSelectionType,
}

/**
 * 类型验证器集合
 */
export const TypeValidators = {
  validateGridOptions,
  validateColumnConfig,
}

/**
 * 类型断言器集合
 */
export const TypeAsserters = {
  assertIsString,
  assertIsNumber,
  assertIsObject,
}

/**
 * 类型工具集合
 */
export const TypeUtils = {
  // JSON操作
  safeParseJSON,
  safeStringify,
  
  // 类型转换
  safeToNumber,
  safeToString,
  
  // 其他工具
  getTypedProperty,
  withTypeCheck,
}
