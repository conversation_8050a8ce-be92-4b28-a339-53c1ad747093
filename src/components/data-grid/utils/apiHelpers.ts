import type { ModelApi } from '@/api/apiService'
import type { QueryParams } from '@/types/api/queryParams'

/**
 * 获取模型元数据
 * @param modelApi 模型API实例
 * @returns 元数据
 */
async function getMetadata(modelApi: ModelApi) {
  const metadata = await modelApi.getMetadata()
  return metadata
}

/**
 * 获取列表数据
 * @param modelApi 模型API实例
 * @param params 查询参数
 * @returns 列表数据
 */
async function getList(modelApi: ModelApi, params: QueryParams) {
  return await modelApi.getList(params)
}

export const apiHelpers = {
  getMetadata,
  getList,
}
