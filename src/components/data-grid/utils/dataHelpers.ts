import type { Model<PERSON><PERSON> } from '@/api/apiService'
import type { QueryParams } from '@/types/api/queryParams'
import type { GridOptions } from '../types'
import { ref, type Ref } from 'vue'
import { performanceMonitor } from './performanceMonitor'

async function getDataGridData(
  gridOptions: Ref<GridOptions>,
  modelApi: ModelApi,
  customQueryParams?: QueryParams
) {
  // 如果提供了自定义查询参数，使用它；否则使用 gridOptions 中的参数
  const queryParams = customQueryParams || {
    offset: 0,
    limit: 20,
    ...gridOptions.value.toolbarOptions?.queryParams,
  }

  // ====== 关键逻辑：自动去除空条件 filters 字段 ======
  if (
    queryParams.filters &&
    Array.isArray(queryParams.filters.conditions) &&
    queryParams.filters.conditions.length === 0
  ) {
    delete queryParams.filters
  }
  // ====== END ======

  try {
    const queryData = await modelApi.getList(queryParams)

    // 确保数据存在且格式正确
    if (queryData && queryData.total >= 0 && Array.isArray(queryData.items)) {
      gridOptions.value.data = queryData.items

      gridOptions.value.toolbarOptions.total = queryData.total

      // 记录组件数据大小到性能监控器
      const rows = queryData.items.length
      const cols = gridOptions.value.columns?.length || 0
      const componentId = modelApi.moduleModel || 'unknown'
      performanceMonitor.recordComponentDataSize(componentId, rows, cols)

      // 更新 gridOptions 中的 queryParams，确保组件状态同步
      if (customQueryParams) {
        gridOptions.value.toolbarOptions.queryParams = {
          ...gridOptions.value.toolbarOptions.queryParams,
          ...customQueryParams,
        }
      }
    } else {
      gridOptions.value.toolbarOptions.total = 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    gridOptions.value.data = []
    // 重置 total 为 0
    if (gridOptions.value.toolbarOptions?.total) {
      const total = gridOptions.value.toolbarOptions.total || 0
      if (typeof total === 'object' && total !== null) {
        ;(total as { value: number }).value = 0
      } else {
        gridOptions.value.toolbarOptions.total = 0
      }
    }
  }
}

export const dataHelpers = {
  getDataGridData,
}
