/**
 * CompositeRenderer 使用示例
 * 
 * 这个文件展示了如何使用CompositeRenderer创建各种复合列
 */

import { createPluginManager } from '../index'

// 创建插件管理器
const manager = createPluginManager()
const columnHelper = manager.getColumnHelper()

// 示例数据
const sampleData = [
  {
    id: 1,
    name: '张三',
    email: 'zhang<PERSON>@example.com',
    phone: '13800138000',
    avatar: 'https://example.com/avatar1.jpg',
    department: '技术部',
    position: '前端工程师',
    salary: 15000,
    status: 'active',
    canEdit: true,
    canDelete: false
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://example.com/avatar2.jpg',
    department: '产品部',
    position: '产品经理',
    salary: 18000,
    status: 'inactive',
    canEdit: true,
    canDelete: true
  }
]

// 示例1: 基础用户信息列
export const basicUserColumn = columnHelper.composite('name', '用户信息', {
  main: { field: 'name' },
  subs: {
    items: [
      { field: 'email' },
      { field: 'phone' }
    ],
    layout: 'horizontal',
    separator: ' | '
  }
})

// 示例2: 带头像的用户列
export const userWithAvatarColumn = columnHelper.composite('name', '用户', {
  main: { field: 'name' },
  icon: {
    type: 'avatar',
    avatarField: 'avatar',
    nameField: 'name',
    size: 40
  },
  subs: {
    items: [
      { field: 'department' },
      { field: 'position' }
    ],
    layout: 'vertical'
  }
})

// 示例3: 带操作按钮的员工列
export const employeeColumn = columnHelper.composite('name', '员工信息', {
  main: {
    field: 'name',
    formatter: (value, row) => `${value} (${row.position})`
  },
  icon: {
    type: 'avatar',
    avatarField: 'avatar',
    size: 36
  },
  subs: {
    items: [
      {
        field: 'salary',
        formatter: (value) => `薪资: ¥${value.toLocaleString()}`
      },
      { field: 'department' }
    ]
  },
  actions: [
    {
      icon: 'mdi:pencil',
      tooltip: '编辑员工',
      variant: 'outline',
      onClick: (row) => {
        console.log('编辑员工:', row.name)
        // 这里可以打开编辑对话框
      },
      condition: (row) => row.canEdit
    },
    {
      icon: 'mdi:delete',
      tooltip: '删除员工',
      variant: 'destructive',
      onClick: (row) => {
        console.log('删除员工:', row.name)
        // 这里可以显示确认对话框
      },
      condition: (row) => row.canDelete
    },
    {
      icon: 'mdi:eye',
      tooltip: '查看详情',
      variant: 'ghost',
      onClick: (row) => {
        console.log('查看员工详情:', row.name)
        // 这里可以打开详情页面
      }
    }
  ],
  showActionsCount: 2,
  enableHover: true,
  width: 280
})

// 示例4: 产品信息列
export const productColumn = columnHelper.composite('name', '产品', {
  main: {
    field: 'name',
    className: 'font-semibold text-primary'
  },
  icon: {
    type: 'image',
    imageField: 'thumbnail',
    size: 48
  },
  subs: {
    items: [
      {
        field: 'price',
        formatter: (value) => `¥${value.toFixed(2)}`,
        className: 'text-green-600 font-medium'
      },
      {
        field: 'stock',
        formatter: (value) => `库存: ${value}`,
        condition: (row) => row.stock !== undefined
      }
    ]
  },
  actions: [
    {
      icon: 'mdi:cart-plus',
      tooltip: '加入购物车',
      onClick: (row) => console.log('加入购物车:', row.name)
    }
  ]
})

// 示例5: 联系人列表
export const contactColumn = columnHelper.composite('name', '联系人', {
  main: { field: 'name' },
  icon: {
    type: 'icon',
    icon: 'mdi:account-circle',
    size: 32
  },
  subs: {
    items: [
      {
        field: 'email',
        formatter: (value) => value,
        className: 'text-blue-600'
      },
      {
        field: 'phone',
        formatter: (value) => value,
        className: 'text-gray-600'
      }
    ],
    layout: 'vertical'
  },
  actions: [
    {
      icon: 'mdi:email',
      tooltip: '发送邮件',
      onClick: (row) => {
        window.open(`mailto:${row.email}`)
      }
    },
    {
      icon: 'mdi:phone',
      tooltip: '拨打电话',
      onClick: (row) => {
        window.open(`tel:${row.phone}`)
      }
    }
  ],
  enableHover: true
})

// 示例6: 文件列表
export const fileColumn = columnHelper.composite('name', '文件', {
  main: {
    field: 'name',
    formatter: (value, row) => {
      const extension = value.split('.').pop()?.toUpperCase()
      return `${value} ${extension ? `[${extension}]` : ''}`
    }
  },
  icon: {
    type: 'icon',
    icon: 'mdi:file-document',
    size: 24
  },
  subs: {
    items: [
      {
        field: 'size',
        formatter: (value) => {
          const sizes = ['B', 'KB', 'MB', 'GB']
          let size = value
          let unit = 0
          while (size >= 1024 && unit < sizes.length - 1) {
            size /= 1024
            unit++
          }
          return `${size.toFixed(1)} ${sizes[unit]}`
        }
      },
      {
        field: 'modifiedAt',
        formatter: (value) => new Date(value).toLocaleDateString()
      }
    ]
  },
  actions: [
    {
      icon: 'mdi:download',
      tooltip: '下载',
      onClick: (row) => console.log('下载文件:', row.name)
    },
    {
      icon: 'mdi:share',
      tooltip: '分享',
      onClick: (row) => console.log('分享文件:', row.name)
    }
  ]
})

// 导出所有示例列配置
export const exampleColumns = {
  basicUserColumn,
  userWithAvatarColumn,
  employeeColumn,
  productColumn,
  contactColumn,
  fileColumn
}

// 使用示例的完整表格配置
export const exampleTableConfig = {
  columns: [
    userWithAvatarColumn,
    employeeColumn
  ],
  data: sampleData
}
