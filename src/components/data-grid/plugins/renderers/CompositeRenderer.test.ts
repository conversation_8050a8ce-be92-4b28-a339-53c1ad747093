/**
 * CompositeRenderer 测试文件
 * 
 * 验证CompositeRenderer在插件系统中的注册和使用
 */

import { describe, it, expect } from 'vitest'
import { createPluginManager } from '../index'

describe('CompositeRenderer Plugin', () => {
  let manager: ReturnType<typeof createPluginManager>
  let columnHelper: ReturnType<typeof manager.getColumnHelper>

  beforeEach(() => {
    manager = createPluginManager()
    columnHelper = manager.getColumnHelper()
  })

  it('应该正确注册CompositeRenderer', () => {
    // 检查渲染器是否已注册
    const renderer = manager.getRenderer('CompositeRenderer')
    expect(renderer).toBeDefined()
    expect(renderer?.name).toBe('CompositeRenderer')
    expect(renderer?.component).toBeDefined()
  })

  it('应该有composite方法在columnHelper中', () => {
    // 检查composite方法是否存在
    expect(typeof columnHelper.composite).toBe('function')
  })

  it('应该能创建基础的composite列配置', () => {
    const column = columnHelper.composite('name', '用户信息')
    
    expect(column).toBeDefined()
    expect(column.field).toBe('name')
    expect(column.title).toBe('用户信息')
    expect(column.plugin).toBe('CompositeRenderer')
    expect(column.pluginConfig).toBeDefined()
  })

  it('应该能创建带配置的composite列', () => {
    const column = columnHelper.composite('name', '用户信息', {
      main: { field: 'fullName' },
      subs: {
        items: [
          { field: 'email' },
          { field: 'phone' }
        ],
        layout: 'vertical'
      },
      icon: {
        type: 'avatar',
        size: 40
      },
      actions: [
        {
          icon: 'mdi:pencil',
          tooltip: '编辑',
          onClick: () => {}
        }
      ],
      width: 300
    })

    expect(column.field).toBe('name')
    expect(column.title).toBe('用户信息')
    expect(column.plugin).toBe('CompositeRenderer')
    expect(column.width).toBe(300)
    
    // 检查配置是否正确合并
    const config = column.pluginConfig
    expect(config.main.field).toBe('fullName')
    expect(config.subs.layout).toBe('vertical')
    expect(config.icon.type).toBe('avatar')
    expect(config.icon.size).toBe(40)
    expect(config.actions).toHaveLength(1)
    expect(config.actions[0].icon).toBe('mdi:pencil')
  })

  it('应该使用默认配置', () => {
    const column = columnHelper.composite('name', '用户信息')
    const config = column.pluginConfig

    // 检查默认配置
    expect(config.main.field).toBe('name')
    expect(config.subs.items).toEqual([])
    expect(config.subs.layout).toBe('horizontal')
    expect(config.subs.separator).toBe('·')
    expect(config.showActionsCount).toBe(1)
    expect(config.enableHover).toBe(true)
  })

  it('应该正确合并用户配置和默认配置', () => {
    const column = columnHelper.composite('name', '用户信息', {
      subs: {
        items: [{ field: 'email' }],
        separator: ' | '
        // layout 未指定，应该使用默认值
      },
      showActionsCount: 2
      // enableHover 未指定，应该使用默认值
    })

    const config = column.pluginConfig
    expect(config.subs.items).toHaveLength(1)
    expect(config.subs.separator).toBe(' | ')
    expect(config.subs.layout).toBe('horizontal') // 默认值
    expect(config.showActionsCount).toBe(2)
    expect(config.enableHover).toBe(true) // 默认值
  })

  it('应该有正确的渲染函数', () => {
    const column = columnHelper.composite('name', '用户信息')
    
    expect(column.slots).toBeDefined()
    expect(column.slots?.default).toBeDefined()
    expect(typeof column.slots?.default?.render).toBe('function')
  })

  it('渲染函数应该返回正确的组件标识', () => {
    const column = columnHelper.composite('name', '用户信息')
    const renderFn = column.slots?.default?.render

    if (renderFn) {
      const result = renderFn({
        value: 'John Doe',
        row: { name: 'John Doe', email: '<EMAIL>' },
        column: {}
      })

      expect(typeof result).toBe('string')
      expect(result).toMatch(/^__COMPONENT__:CompositeRenderer:/)
      
      // 解析组件属性
      const [, , propsJson] = result.split(':')
      const props = JSON.parse(propsJson)
      
      expect(props.value).toBe('John Doe')
      expect(props.row.name).toBe('John Doe')
      expect(props.field).toBe('name')
      expect(props.configId).toBeDefined()
    }
  })

  it('应该在已注册的渲染器列表中', () => {
    const renderers = manager.getRegisteredRenderers()
    expect(renderers).toContain('CompositeRenderer')
  })

  it('应该在已注册的组件列表中', () => {
    const components = manager.getRegisteredComponents()
    expect(components).toContain('CompositeRenderer')
  })
})

describe('CompositeRenderer 配置验证', () => {
  let columnHelper: ReturnType<typeof createPluginManager>['getColumnHelper']

  beforeEach(() => {
    const manager = createPluginManager()
    columnHelper = manager.getColumnHelper()
  })

  it('应该支持复杂的图标配置', () => {
    const column = columnHelper.composite('name', '用户', {
      icon: {
        type: 'avatar',
        avatarField: 'profilePic',
        nameField: 'fullName',
        size: 48
      }
    })

    const config = column.pluginConfig
    expect(config.icon.type).toBe('avatar')
    expect(config.icon.avatarField).toBe('profilePic')
    expect(config.icon.nameField).toBe('fullName')
    expect(config.icon.size).toBe(48)
  })

  it('应该支持复杂的操作配置', () => {
    const mockOnClick = vi.fn()
    const mockCondition = vi.fn().mockReturnValue(true)

    const column = columnHelper.composite('name', '用户', {
      actions: [
        {
          key: 'edit',
          icon: 'mdi:pencil',
          tooltip: '编辑用户',
          variant: 'outline',
          size: 'sm',
          onClick: mockOnClick,
          condition: mockCondition
        }
      ]
    })

    const config = column.pluginConfig
    expect(config.actions).toHaveLength(1)
    
    const action = config.actions[0]
    expect(action.key).toBe('edit')
    expect(action.icon).toBe('mdi:pencil')
    expect(action.tooltip).toBe('编辑用户')
    expect(action.variant).toBe('outline')
    expect(action.size).toBe('sm')
    expect(action.onClick).toBe(mockOnClick)
    expect(action.condition).toBe(mockCondition)
  })

  it('应该支持子内容的格式化函数', () => {
    const mockFormatter = vi.fn().mockReturnValue('formatted value')

    const column = columnHelper.composite('name', '用户', {
      subs: {
        items: [
          {
            field: 'salary',
            formatter: mockFormatter,
            className: 'text-green-600'
          }
        ]
      }
    })

    const config = column.pluginConfig
    const subItem = config.subs.items[0]
    expect(subItem.field).toBe('salary')
    expect(subItem.formatter).toBe(mockFormatter)
    expect(subItem.className).toBe('text-green-600')
  })
})
