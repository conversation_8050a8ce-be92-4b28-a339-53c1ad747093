# CompositeRenderer 复合渲染器

复合渲染器是一个功能强大的数据网格列渲染器，支持图标、主内容、子内容和操作按钮的灵活组合显示。

## 功能特性

- 🎨 **多内容组合**: 支持主内容、子内容、图标和操作按钮的组合显示
- 🖼️ **图标支持**: 支持普通图标、图片和头像三种图标类型
- 🎭 **布局模式**: 支持水平和垂直两种布局方式
- 🎯 **操作按钮**: 支持直接显示的操作按钮和下拉菜单更多操作
- 🖱️ **悬停效果**: 智能的悬停显示/隐藏操作按钮
- 📱 **响应式**: 完全响应式设计，支持动态配置
- 🎛️ **高度可配置**: 支持条件显示、自定义样式和格式化函数

## 基本用法

### 1. 简单的复合列

```typescript
import { createPluginManager } from '@/components/data-grid/plugins'

const manager = createPluginManager()
const columnHelper = manager.getColumnHelper()

// 创建简单的复合列
const userColumn = columnHelper.composite('name', '用户信息', {
  main: { field: 'name' },
  subs: {
    items: [
      { field: 'email', label: '邮箱' },
      { field: 'phone', label: '电话' }
    ]
  }
})
```

### 2. 带头像的复合列

```typescript
const userColumn = columnHelper.composite('name', '用户信息', {
  main: { field: 'name' },
  icon: {
    type: 'avatar',
    avatarField: 'avatar',
    nameField: 'name',
    size: 40
  },
  subs: {
    items: [
      { field: 'email' },
      { field: 'department' }
    ],
    layout: 'vertical'
  }
})
```

### 3. 带操作按钮的复合列

```typescript
const userColumn = columnHelper.composite('name', '用户信息', {
  main: { field: 'name' },
  subs: {
    items: [{ field: 'email' }]
  },
  actions: [
    {
      icon: 'mdi:pencil',
      tooltip: '编辑',
      variant: 'outline',
      onClick: (row) => console.log('编辑用户:', row)
    },
    {
      icon: 'mdi:delete',
      tooltip: '删除',
      variant: 'destructive',
      onClick: (row) => console.log('删除用户:', row),
      condition: (row) => row.canDelete
    }
  ],
  showActionsCount: 1,
  enableHover: true
})
```

### 4. 完整配置示例

```typescript
const productColumn = columnHelper.composite('name', '产品信息', {
  main: {
    field: 'name',
    formatter: (value, row) => `${value} (${row.sku})`,
    className: 'font-bold text-primary'
  },
  icon: {
    type: 'image',
    imageField: 'thumbnail',
    size: 48
  },
  subs: {
    items: [
      {
        field: 'price',
        formatter: (value) => `¥${value.toFixed(2)}`,
        className: 'text-green-600 font-medium'
      },
      {
        field: 'stock',
        formatter: (value) => `库存: ${value}`,
        condition: (row) => row.showStock
      },
      {
        field: 'category',
        formatter: (value) => `分类: ${value}`
      }
    ],
    layout: 'horizontal',
    separator: ' | '
  },
  actions: [
    {
      icon: 'mdi:eye',
      tooltip: '查看详情',
      onClick: (row) => viewProduct(row.id)
    },
    {
      icon: 'mdi:pencil',
      tooltip: '编辑',
      onClick: (row) => editProduct(row.id)
    },
    {
      icon: 'mdi:content-copy',
      tooltip: '复制',
      onClick: (row) => copyProduct(row.id)
    }
  ],
  showActionsCount: 2,
  enableHover: true,
  width: 300
})
```

## 配置选项

### main (主内容配置)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| field | string | 'name' | 数据字段名 |
| formatter | function | - | 格式化函数 |
| className | string | - | 自定义CSS类名 |
| style | object | - | 自定义样式 |

### subs (子内容配置)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| items | array | [] | 子内容项数组 |
| layout | 'horizontal' \| 'vertical' | 'horizontal' | 布局方式 |
| separator | string | '·' | 水平布局时的分隔符 |

#### subs.items 子内容项配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| field | string | - | 数据字段名 |
| label | string | - | 显示标签 |
| formatter | function | - | 格式化函数 |
| condition | function | - | 显示条件函数 |
| className | string | - | 自定义CSS类名 |

### icon (图标配置)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | 'icon' \| 'image' \| 'avatar' | - | 图标类型 |
| icon | string | - | 图标名称 (type='icon'时) |
| imageField | string | - | 图片字段名 (type='image'时) |
| avatarField | string | 'avatar' | 头像字段名 (type='avatar'时) |
| nameField | string | 'name' | 姓名字段名 (type='avatar'时) |
| size | number | 32 | 图标尺寸 (像素) |

### actions (操作按钮配置)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| key | string | - | 唯一标识 |
| text | string | - | 按钮文本 |
| icon | string | - | 图标名称 |
| tooltip | string | - | 提示文本 |
| variant | string | 'outline' | 按钮样式变体 |
| size | string | 'sm' | 按钮尺寸 |
| onClick | function | - | 点击事件处理函数 |
| condition | function | - | 显示条件函数 |

### 其他配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showActionsCount | number | 1 | 直接显示的操作按钮数量 |
| enableHover | boolean | true | 是否启用悬停显示操作 |
| width | number | 200 | 列宽度 |

## 注意事项

1. **性能优化**: 格式化函数应该尽量简单，避免复杂计算
2. **条件显示**: 使用condition函数可以根据数据动态显示/隐藏内容
3. **操作按钮**: 超出showActionsCount的操作会自动收入下拉菜单
4. **悬停效果**: enableHover为true时，操作按钮仅在悬停时显示
5. **图标尺寸**: 图标尺寸会自动转换为Tailwind CSS类名

## 类型定义

```typescript
interface CompositeConfig {
  main?: MainContentConfig
  subs?: SubContentConfig
  icon?: IconConfig
  actions?: ActionButton[]
  showActionsCount?: number
  enableHover?: boolean
  width?: number
}
```

完整的类型定义请参考组件源码。
