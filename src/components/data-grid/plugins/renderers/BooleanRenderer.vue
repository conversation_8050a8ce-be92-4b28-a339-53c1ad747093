<template>
  <div class="inline-flex items-center">
    <!-- Badge 变体 -->
    <Badge v-if="variant === 'badge'" :class="badgeClasses">
      <Icon v-if="currentConfig.icon" :icon="currentConfig.icon" class="mr-1 inline-block" />
      {{ currentConfig.text }}
    </Badge>
    
    <!-- Switch 变体 -->
    <Switch 
      v-else-if="variant === 'switch'" 
      :checked="booleanValue" 
      :disabled="true"
      class="pointer-events-none"
    />
    
    <!-- Icon 变体 -->
    <Icon 
      v-else-if="variant === 'icon' && currentConfig.icon" 
      :icon="currentConfig.icon" 
      :class="iconClasses"
    />
    
    <!-- Text 变体 -->
    <span v-else :class="textClasses">
      {{ currentConfig.text }}
    </span>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'BooleanRender<PERSON>',
})

import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Icon } from '@iconify/vue'
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface Props {
  value?: any
  row: any
  config?: {
    variant?: 'switch' | 'badge' | 'icon' | 'text'
    trueText?: string
    falseText?: string
    trueConfig?: { text: string; type?: string; icon?: string }
    falseConfig?: { text: string; type?: string; icon?: string }
    [key: string]: any
  }
  field?: string
}

const props = defineProps<Props>()

// 获取布尔值
const value = computed(() => {
  return props.field ? props.row?.[props.field] : props.value
})

const booleanValue = computed(() => {
  const val = value.value
  if (typeof val === 'boolean') return val
  if (typeof val === 'number') return val === 1
  if (typeof val === 'string') return val === 'true' || val === '1' || val === 'yes'
  return false
})

const variant = computed(() => props.config?.variant || 'badge')

// 获取当前配置
const currentConfig = computed(() => {
  const isTrue = booleanValue.value
  
  if (isTrue) {
    return props.config?.trueConfig || {
      text: props.config?.trueText || '是',
      type: 'success',
      icon: 'check-circle'
    }
  } else {
    return props.config?.falseConfig || {
      text: props.config?.falseText || '否',
      type: 'danger',
      icon: 'x-circle'
    }
  }
})

// Badge 样式
const badgeClasses = computed(() => {
  const type = currentConfig.value.type || 'default'
  
  return cn('inline-flex items-center', {
    'bg-green-100 text-green-800': type === 'success',
    'bg-red-100 text-red-800': type === 'danger',
    'bg-blue-100 text-blue-800': type === 'primary',
    'bg-yellow-100 text-yellow-800': type === 'warning',
    'bg-gray-100 text-gray-800': type === 'info',
    'bg-muted text-muted-foreground': type === 'default',
  })
})

// Icon 样式
const iconClasses = computed(() => {
  const type = currentConfig.value.type || 'default'
  
  return cn('w-4 h-4', {
    'text-green-600': type === 'success',
    'text-red-600': type === 'danger',
    'text-blue-600': type === 'primary',
    'text-yellow-600': type === 'warning',
    'text-gray-600': type === 'info',
    'text-muted-foreground': type === 'default',
  })
})

// Text 样式
const textClasses = computed(() => {
  const type = currentConfig.value.type || 'default'
  
  return cn('text-sm', {
    'text-green-600': type === 'success',
    'text-red-600': type === 'danger',
    'text-blue-600': type === 'primary',
    'text-yellow-600': type === 'warning',
    'text-gray-600': type === 'info',
    'text-muted-foreground': type === 'default',
  })
})
</script>
