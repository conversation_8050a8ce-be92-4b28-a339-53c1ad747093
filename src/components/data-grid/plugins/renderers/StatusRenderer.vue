<template>
  <!-- Badge 变体 -->
  <Badge v-if="variant === 'badge'" :class="badgeClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </Badge>

  <!-- Dot 变体 -->
  <div v-else-if="variant === 'dot'" :class="dotContainerClasses">
    <div :class="dotClasses"></div>
    <span :class="dotTextClasses">{{ status.text }}</span>
  </div>

  <!-- Text 变体 -->
  <span v-else-if="variant === 'text'" :class="textClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </span>

  <!-- Progress 变体 -->
  <div v-else-if="variant === 'progress'" :class="progressContainerClasses">
    <div class="w-full">
      <div class="flex items-center justify-between mb-1">
        <span :class="progressTextClasses">{{ status.text }}</span>
        <span v-if="progressPercentage !== undefined" class="text-xs text-gray-500">
          {{ progressPercentage }}%
        </span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          :class="progressBarClasses" 
          :style="{ width: `${progressPercentage || 0}%` }"
        ></div>
      </div>
    </div>
  </div>

  <!-- 默认回退到 Badge -->
  <Badge v-else :class="badgeClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </Badge>
</template>

<script setup lang="ts">
defineOptions({
  name: 'StatusRenderer',
})

import { Badge } from '@/components/ui/badge'
import { Icon } from '@iconify/vue'
import { computed } from 'vue'
import { cn } from '@/lib/utils'

// 状态配置类型
interface StatusConfig {
  text: string
  key?: string
  type?: string
  icon?: string
  progress?: number
}

interface Props {
  value?: any
  row: any
  config?: {
    statusMap?: Record<string | number, StatusConfig>
    defaultStatus?: StatusConfig
    variant?: 'badge' | 'dot' | 'text' | 'progress'
    progress?: number
    [key: string]: any
  }
  field?: string
}

const props = defineProps<Props>()

// 获取状态值
const value = computed(() => {
  return props.field ? props.row?.[props.field] : props.value
})

// 获取状态配置（现在由 DataGrid 预处理）
const statusMap = computed(() => props.config?.statusMap || {})

const defaultStatus = computed(
  () => props.config?.defaultStatus || { text: 'Unknown', type: 'default' }
)

// 获取渲染变体
const variant = computed(() => props.config?.variant || 'badge')

// 获取当前状态
const status = computed(() => {
  const currentValue = value.value
  if (statusMap.value[currentValue]) {
    return statusMap.value[currentValue]
  }
  return defaultStatus.value
})

// 获取进度百分比（仅用于 progress 变体）
const progressPercentage = computed(() => {
  return status.value.progress || props.config?.progress
})

// 获取状态类型颜色配置
const getColorClasses = (type: string) => {
  const colors = {
    primary: { bg: 'bg-blue-100', text: 'text-blue-800', dot: 'bg-blue-500', border: 'border-blue-200' },
    success: { bg: 'bg-green-100', text: 'text-green-800', dot: 'bg-green-500', border: 'border-green-200' },
    warning: { bg: 'bg-yellow-100', text: 'text-yellow-800', dot: 'bg-yellow-500', border: 'border-yellow-200' },
    danger: { bg: 'bg-red-100', text: 'text-red-800', dot: 'bg-red-500', border: 'border-red-200' },
    info: { bg: 'bg-gray-100', text: 'text-gray-800', dot: 'bg-gray-500', border: 'border-gray-200' },
    default: { bg: 'bg-muted', text: 'text-muted-foreground', dot: 'bg-gray-400', border: 'border-gray-200' },
  }
  return colors[type as keyof typeof colors] || colors.default
}

// Badge 样式类
const badgeClasses = computed(() => {
  const statusType = status.value.type || 'default'
  const colors = getColorClasses(statusType)
  
  return cn('inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', colors.bg, colors.text)
})

// Dot 容器样式类
const dotContainerClasses = computed(() => {
  return cn('inline-flex items-center space-x-2')
})

// Dot 样式类
const dotClasses = computed(() => {
  const statusType = status.value.type || 'default'
  const colors = getColorClasses(statusType)
  
  return cn('w-2 h-2 rounded-full', colors.dot)
})

// Dot 文本样式类
const dotTextClasses = computed(() => {
  const statusType = status.value.type || 'default'
  const colors = getColorClasses(statusType)
  
  return cn('text-sm font-medium', colors.text)
})

// Text 样式类
const textClasses = computed(() => {
  const statusType = status.value.type || 'default'
  const colors = getColorClasses(statusType)
  
  return cn('inline-flex items-center text-sm font-medium', colors.text)
})

// Progress 容器样式类
const progressContainerClasses = computed(() => {
  return cn('w-full')
})

// Progress 文本样式类
const progressTextClasses = computed(() => {
  const statusType = status.value.type || 'default'
  const colors = getColorClasses(statusType)
  
  return cn('text-sm font-medium', colors.text)
})

// Progress 进度条样式类
const progressBarClasses = computed(() => {
  const statusType = status.value.type || 'default'
  const colors = getColorClasses(statusType)
  
  return cn('h-2 rounded-full transition-all duration-300', colors.dot)
})
</script>
