<template>
  <div class="flex items-center justify-center gap-1">
    <!-- 直接显示的操作按钮 -->
    <Button
      v-for="action in directActions"
      :key="action.text || action.icon || action.tooltip"
      :variant="getButtonVariant(action)"
      :size="getButtonSize(action)"
      :title="action.tooltip"
      :disabled="isActionDisabled(action)"
      class="p-1 gap-0"
      @click="handleActionClick(action)"
    >
      <Icon v-if="action.icon" :icon="action.icon" class="w-4 h-4" />
      <span v-if="action.text">{{ action.text }}</span>
    </Button>

    <!-- 更多操作下拉菜单 -->
    <DropdownMenu v-if="hasMoreActions">
      <DropdownMenuTrigger as-child>
        <Button variant="outline" size="sm" class="p-1 gap-0" title="更多操作">
          <Icon icon="mdi:dots-horizontal" class="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          v-for="action in dropdownActions"
          :key="action.text || action.icon || action.tooltip"
          :disabled="isActionDisabled(action)"
          @select="handleActionClick(action)"
        >
          <Icon v-if="action.icon" :icon="action.icon" class="w-4 h-4 mr-2" />
          <span>{{ action.text || action.tooltip || '操作' }}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ActionsRenderer',
})

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Icon } from '@iconify/vue'
import { computed } from 'vue'

interface ActionConfig {
  text?: string
  icon?: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
  size?: 'small' | 'medium' | 'large' | 'default' | 'sm' | 'lg' | 'icon'
  tooltip?: string
  onClick: (row: any) => void
  condition?: (row: any) => boolean
  disabled?: (row: any) => boolean
  confirm?: string | ((row: any) => string)
}

interface Props {
  value?: any
  row: any
  config?: {
    actions?: ActionConfig[]
    layout?: 'horizontal' | 'dropdown'
    showActionsCount?: number
    [key: string]: any
  }
  field?: string
}

const props = defineProps<Props>()

const actions = computed(() => props.config?.actions || [])

// 过滤可见的操作
const visibleActions = computed(() => {
  return actions.value.filter((action) => {
    if (action.condition) {
      return action.condition(props.row)
    }
    return true
  })
})

// 获取直接显示的操作按钮
const directActions = computed(() => {
  const showCount = props.config?.showActionsCount || 2
  const layout = props.config?.layout || 'horizontal'

  if (layout === 'dropdown') {
    return []
  }

  return visibleActions.value.slice(0, showCount)
})

// 获取下拉菜单中的操作按钮
const dropdownActions = computed(() => {
  const showCount = props.config?.showActionsCount || 2
  const layout = props.config?.layout || 'horizontal'

  if (layout === 'dropdown') {
    return visibleActions.value
  }

  return visibleActions.value.slice(showCount)
})

// 是否有更多操作
const hasMoreActions = computed(() => {
  return dropdownActions.value.length > 0
})

// 获取按钮变体
const getButtonVariant = (action: ActionConfig) => {
  if (action.variant) return action.variant

  // 根据类型映射变体
  switch (action.type) {
    case 'primary':
      return 'default'
    case 'success':
      return 'default'
    case 'warning':
      return 'outline'
    case 'danger':
      return 'destructive'
    case 'info':
      return 'secondary'
    default:
      return 'outline'
  }
}

// 获取按钮大小
const getButtonSize = (action: ActionConfig) => {
  const size = action.size || 'sm'
  // 映射到 Button 组件支持的大小
  switch (size) {
    case 'small':
      return 'sm'
    case 'medium':
      return 'default'
    case 'large':
      return 'lg'
    default:
      return size as 'default' | 'sm' | 'lg' | 'icon' | 'xs'
  }
}

// 检查操作是否禁用
const isActionDisabled = (action: ActionConfig) => {
  if (action.disabled && typeof action.disabled === 'function') {
    return action.disabled(props.row)
  }
  return false
}

// 处理操作点击
const handleActionClick = async (action: ActionConfig) => {
  try {
    // 检查是否禁用
    if (isActionDisabled(action)) {
      return
    }

    // 显示确认对话框
    if (action.confirm) {
      const confirmMessage =
        typeof action.confirm === 'function'
          ? action.confirm(props.row)
          : action.confirm

      if (!window.confirm(confirmMessage)) {
        return
      }
    }

    // 执行操作
    if (action.onClick && typeof action.onClick === 'function') {
      action.onClick(props.row)
    }
  } catch (error) {
    console.error('Action execution failed:', error)
  }
}
</script>
