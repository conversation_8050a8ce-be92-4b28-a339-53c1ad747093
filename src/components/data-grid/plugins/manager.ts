/**
 * 插件管理器
 *
 * 提供轻量级的组件注册和列配置功能，替代复杂的插件系统。
 * 支持状态、布尔值、链接、操作等常用渲染器，以及丰富的 Helper 方法。
 *
 * @example
 * ```typescript
 * const manager = new PluginManager()
 * const columnHelper = manager.getColumnHelper()
 *
 * // 创建状态列
 * const statusColumn = columnHelper.status('status', '状态', {
 *   statusMap: { active: { text: '活跃', type: 'success' } }
 * })
 * ```
 */

import { markRaw, type Component } from 'vue'

// 简单的配置存储，用于存储包含函数的配置
const configStore = new Map<string, any>()

// 生成配置ID
const generateConfigId = () => {
  return `config_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// 渲染器定义
export interface PluginRenderer {
  name: string
  component: Component
  defaultConfig?: Record<string, any>
  defaultWidth?: number
}

// 列配置
export interface PluginColumnConfig {
  field: string
  title: string
  width?: number
  plugin?: string
  pluginConfig?: Record<string, any>
  slots?: {
    default?: {
      render?: (params: any) => any
    }
  }
  [key: string]: any
}

/**
 * 插件管理器类
 */
export class PluginManager {
  private components = new Map<string, Component>()
  private renderers = new Map<string, PluginRenderer>()
  private columnHelper: PluginHelper

  constructor() {
    this.columnHelper = new PluginHelper(this)

    // 将配置存储暴露到全局，供 DataGrid 使用
    if (typeof window !== 'undefined') {
      ;(window as any).__simpleConfigStore = configStore
    }
  }

  /**
   * 注册组件
   */
  registerComponent(name: string, component: Component): void {
    this.components.set(name, markRaw(component))
  }

  /**
   * 获取组件
   */
  getComponent(name: string): Component | undefined {
    return this.components.get(name)
  }

  /**
   * 注册渲染器
   */
  registerRenderer(renderer: PluginRenderer): void {
    this.renderers.set(renderer.name, renderer)
    // 同时注册组件
    this.registerComponent(renderer.name, renderer.component)
  }

  /**
   * 获取渲染器
   */
  getRenderer(name: string): PluginRenderer | undefined {
    return this.renderers.get(name)
  }

  /**
   * 获取列助手
   */
  getColumnHelper(): PluginHelper {
    return this.columnHelper
  }

  /**
   * 获取所有已注册的组件名称
   */
  getRegisteredComponents(): string[] {
    return Array.from(this.components.keys())
  }

  /**
   * 获取所有已注册的渲染器名称
   */
  getRegisteredRenderers(): string[] {
    return Array.from(this.renderers.keys())
  }

  /**
   * 从配置存储中获取配置
   */
  getConfig(configId: string): any {
    return configStore.get(configId)
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.components.clear()
    this.renderers.clear()
    configStore.clear()

    // 清理全局配置存储
    if (typeof window !== 'undefined') {
      delete (window as any).__simpleConfigStore
    }
  }
}

/**
 * 列助手类
 */
export class PluginHelper {
  constructor(private manager: PluginManager) {}

  /**
   * 创建基础列配置
   */
  createColumn(
    field: string,
    title: string,
    plugin?: string,
    config?: Record<string, any>
  ): PluginColumnConfig {
    const column: PluginColumnConfig = {
      field,
      title,
    }

    if (plugin) {
      const renderer = this.manager.getRenderer(plugin)
      column.plugin = plugin
      column.pluginConfig = {
        ...renderer?.defaultConfig,
        ...config,
      }
      if (renderer?.defaultWidth) {
        column.width = config?.width || renderer.defaultWidth
      }

      // 创建渲染函数，返回组件标识符
      column.slots = {
        default: {
          render: (params: any) => {
            // 存储完整配置到配置存储
            const configId = generateConfigId()
            configStore.set(configId, column.pluginConfig)

            // 准备可序列化的组件属性
            const componentProps = {
              value: params.value,
              row: params.row,
              field: field,
              configId: configId,
              // 传递列配置信息，包含 enumInfo 等 metadata 信息
              column: params.column || {},
            }

            // 返回组件渲染标识，让 DataGrid 知道需要渲染组件
            return `__COMPONENT__:${plugin}:${JSON.stringify(componentProps)}`
          },
        },
      }
    }

    // 处理其他列配置属性（如 width, fixed, align 等）
    if (config) {
      Object.keys(config).forEach((key) => {
        if (key !== 'width' && !column.hasOwnProperty(key)) {
          column[key] = config[key]
        }
      })
    }

    return column
  }

  /**
   * 创建状态列
   */
  status(
    field: string,
    title: string,
    config?: {
      statusMap?: Record<
        string | number,
        { text: string; type?: string; icon?: string }
      >
      defaultStatus?: { text: string; type?: string; icon?: string }
      variant?: 'badge' | 'dot' | 'text' | 'progress'
      width?: number
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'StatusRenderer', {
      variant: 'badge',
      autoFromMetadata: config?.statusMap ? false : true,
      ...config,
    })
  }

  /**
   * 创建布尔值列
   */
  boolean(
    field: string,
    title: string,
    config?: {
      variant?: 'switch' | 'badge' | 'icon' | 'text'
      trueText?: string
      falseText?: string
      trueConfig?: { text: string; type?: string; icon?: string }
      falseConfig?: { text: string; type?: string; icon?: string }
      width?: number
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'BooleanRenderer', {
      variant: 'badge',
      trueText: '是',
      falseText: '否',
      ...config,
    })
  }

  /**
   * 创建链接列
   */
  link(
    field: string,
    title: string,
    config?: {
      href?: string | ((row: any) => string)
      target?: '_blank' | '_self' | '_parent' | '_top'
      showExternal?: boolean
      truncate?: boolean
      maxLength?: number
      onClick?: (row: any, event: Event) => boolean | void
      width?: number
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      target: '_blank',
      showExternal: true,
      truncate: false,
      ...config,
    })
  }

  /**
   * 创建操作列
   */
  actions(
    title: string,
    config?: {
      actions?: Array<{
        text: string
        icon?: string
        type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
        onClick: (row: any) => void
        condition?: (row: any) => boolean
      }>
      layout?: 'horizontal' | 'dropdown'
      width?: number
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn('actions', title, 'ActionsRenderer', {
      layout: 'horizontal',
      actions: [],
      ...config,
    })
  }

  /**
   * 创建邮箱列 - 专用Helper
   */
  email(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      truncate?: boolean
      maxLength?: number
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'mail',
      showTypeIcon: true,
      showExternal: false,
      target: '_self',
      truncate: true,
      maxLength: 35,
      ...config,
    })
  }

  /**
   * 创建电话列 - 专用Helper
   */
  phone(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      truncate?: boolean
      maxLength?: number
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'phone',
      showTypeIcon: true,
      showExternal: false,
      target: '_self',
      truncate: false,
      ...config,
    })
  }

  /**
   * 创建网址列 - 专用Helper
   */
  url(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      showExternal?: boolean
      target?: '_blank' | '_self' | '_parent' | '_top'
      truncate?: boolean
      maxLength?: number
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'url',
      showTypeIcon: true,
      showExternal: true,
      target: '_blank',
      truncate: true,
      maxLength: 30,
      template: '🔗 ${value}',
      ...config,
    })
  }

  /**
   * 创建外部链接列 - 专用Helper
   */
  externalLink(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      truncate?: boolean
      maxLength?: number
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'url',
      showTypeIcon: false,
      showExternal: true,
      target: '_blank',
      truncate: true,
      maxLength: 25,
      ...config,
    })
  }

  /**
   * 创建内部链接列 - 专用Helper
   */
  internalLink(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      truncate?: boolean
      maxLength?: number
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'url',
      showTypeIcon: false,
      showExternal: false,
      target: '_self',
      truncate: true,
      maxLength: 30,
      ...config,
    })
  }

  /**
   * 创建微信号列 - 专用Helper
   */
  wechat(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'url',
      showTypeIcon: true,
      showExternal: false,
      target: '_self',
      template: '💬 ${value}',
      onClick: (row: any) => {
        // 默认复制微信号到剪贴板
        if (navigator.clipboard) {
          navigator.clipboard.writeText(row[field])
        }
      },
      ...config,
    })
  }

  /**
   * 创建QQ号列 - 专用Helper
   */
  qq(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'url',
      showTypeIcon: true,
      showExternal: false,
      target: '_self',
      template: '🐧 ${value}',
      href: (row: any) => `tencent://message/?uin=${row[field]}`,
      ...config,
    })
  }

  /**
   * 创建Skype列 - 专用Helper
   */
  skype(
    field: string,
    title: string,
    config?: {
      template?: string
      showTypeIcon?: boolean
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'url',
      showTypeIcon: true,
      showExternal: false,
      target: '_self',
      template: '📞 ${value}',
      href: (row: any) => `skype:${row[field]}?call`,
      ...config,
    })
  }

  /**
   * 创建社交媒体链接列 - 专用Helper
   */
  socialMedia(
    field: string,
    title: string,
    platform: 'twitter' | 'facebook' | 'linkedin' | 'instagram' | 'github',
    config?: {
      template?: string
      showTypeIcon?: boolean
      onClick?: (row: any, event: Event) => boolean | void
      [key: string]: any
    }
  ): PluginColumnConfig {
    const platformConfig = {
      twitter: {
        template: '🐦 ${value}',
        href: (row: any) => `https://twitter.com/${row[field]}`,
      },
      facebook: {
        template: '📘 ${value}',
        href: (row: any) => `https://facebook.com/${row[field]}`,
      },
      linkedin: {
        template: '💼 ${value}',
        href: (row: any) => `https://linkedin.com/in/${row[field]}`,
      },
      instagram: {
        template: '📷 ${value}',
        href: (row: any) => `https://instagram.com/${row[field]}`,
      },
      github: {
        template: '🐙 ${value}',
        href: (row: any) => `https://github.com/${row[field]}`,
      },
    }

    return this.createColumn(field, title, 'LinkRenderer', {
      linkType: 'url',
      showTypeIcon: true,
      showExternal: true,
      target: '_blank',
      ...platformConfig[platform],
      ...config,
    })
  }

  /**
   * 创建复合列 - 支持图标、主内容、子内容和操作按钮的组合显示
   */
  composite(
    field: string,
    title: string,
    config?: {
      main?: {
        field?: string
        formatter?: (value: any, row: any) => string
        className?: string
        style?: Record<string, any>
      }
      subs?: {
        items?: Array<{
          field: string
          label?: string
          formatter?: (value: any, row: any) => string
          condition?: (row: any) => boolean
          className?: string
        }>
        layout?: 'horizontal' | 'vertical'
        separator?: string
      }
      icon?: {
        type?: 'icon' | 'image' | 'avatar'
        icon?: string
        imageField?: string
        avatarField?: string
        nameField?: string
        size?: number
      }
      actions?: Array<{
        key?: string
        text?: string
        icon: string
        tooltip?: string
        variant?:
          | 'link'
          | 'default'
          | 'destructive'
          | 'outline'
          | 'secondary'
          | 'ghost'
        size?: 'icon' | 'default' | 'xs' | 'sm' | 'lg'
        onClick: (row: any) => void
        condition?: (row: any) => boolean
      }>
      showActionsCount?: number
      enableHover?: boolean
      width?: number
      [key: string]: any
    }
  ): PluginColumnConfig {
    return this.createColumn(field, title, 'CompositeRenderer', {
      main: { field: field },
      subs: { items: [], layout: 'horizontal', separator: '·' },
      showActionsCount: 1,
      enableHover: true,
      ...config,
    })
  }
}
