/**
 * 简化的插件系统入口
 * 替代复杂的插件系统，提供基本的渲染器功能
 */

import { PluginManager, type PluginRenderer } from './manager'

// 导入简化的渲染器组件
import StatusRenderer from './renderers/StatusRenderer.vue'
import BooleanRenderer from './renderers/BooleanRenderer.vue'
import LinkRenderer from './renderers/LinkRenderer.vue'
import ActionsRenderer from './renderers/ActionsRenderer.vue'

// 定义核心渲染器
const coreRenderers: PluginRenderer[] = [
  {
    name: 'StatusRenderer',
    component: StatusRenderer,
    defaultConfig: {
      variant: 'badge',
    },
    defaultWidth: 120,
  },
  {
    name: 'BooleanRenderer',
    component: BooleanRenderer,
    defaultConfig: {
      variant: 'badge',
      trueText: '是',
      falseText: '否',
    },
    defaultWidth: 80,
  },
  {
    name: 'LinkRenderer',
    component: LinkRenderer,
    defaultConfig: {
      target: '_blank',
      showExternal: true,
      truncate: false,
    },
    defaultWidth: 150,
  },
  {
    name: 'ActionsRenderer',
    component: ActionsRenderer,
    defaultConfig: {
      layout: 'horizontal',
      actions: [],
    },
    defaultWidth: 120,
  },
]

// 全局插件管理器实例
let globalManager: PluginManager | null = null

/**
 * 创建简化的插件管理器
 */
export function createPluginManager(): PluginManager {
  const manager = new PluginManager()

  // 注册核心渲染器
  coreRenderers.forEach((renderer) => {
    manager.registerRenderer(renderer)
  })

  return manager
}

/**
 * 获取全局插件管理器
 */
export function getGlobalPluginManager(): PluginManager {
  if (!globalManager) {
    globalManager = createPluginManager()
  }
  return globalManager
}

// 导出类型和接口
export type { PluginRenderer, PluginColumnConfig } from './manager'
export { PluginManager, PluginHelper } from './manager'

// 导出渲染器组件
export { StatusRenderer, BooleanRenderer, LinkRenderer, ActionsRenderer }

// 便利函数：创建状态映射
export function createStatusMap() {
  return new StatusMapBuilder()
}

/**
 * 状态映射构建器
 */
export class StatusMapBuilder {
  private statusMap: Record<string | number, any> = {}

  add(
    value: string | number | boolean,
    config: { text: string; type?: string; icon?: string; color?: string }
  ) {
    this.statusMap[String(value)] = config
    return this
  }

  success(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'success', icon })
  }

  warning(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'warning', icon })
  }

  error(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'danger', icon })
  }

  info(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'info', icon })
  }

  build() {
    return { ...this.statusMap }
  }

  reset() {
    this.statusMap = {}
    return this
  }
}

// 常用状态映射
export const CommonStatusMaps = {
  enabledDisabled: createStatusMap()
    .success(1, '启用', 'check-circle')
    .success('enabled', '启用', 'check-circle')
    .success(true, '启用', 'check-circle')
    .error(0, '禁用', 'x-circle')
    .error('disabled', '禁用', 'x-circle')
    .error(false, '禁用', 'x-circle')
    .build(),

  activeInactive: createStatusMap()
    .success('active', '活跃', 'activity')
    .warning('inactive', '非活跃', 'pause-circle')
    .build(),

  orderStatus: createStatusMap()
    .info('pending', '待处理', 'clock')
    .warning('processing', '处理中', 'loader')
    .success('completed', '已完成', 'check-circle')
    .error('cancelled', '已取消', 'x-circle')
    .build(),
}

// 便利函数：创建操作配置
export function createActions() {
  return new ActionsBuilder()
}

/**
 * 操作构建器
 */
export class ActionsBuilder {
  private actions: any[] = []

  add(action: {
    text: string
    icon?: string
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    onClick: (row: any) => void
    condition?: (row: any) => boolean
  }) {
    this.actions.push(action)
    return this
  }

  edit(onClick: (row: any) => void, condition?: (row: any) => boolean) {
    return this.add({
      text: '编辑',
      icon: 'edit',
      type: 'primary',
      onClick,
      condition,
    })
  }

  delete(onClick: (row: any) => void, condition?: (row: any) => boolean) {
    return this.add({
      text: '删除',
      icon: 'trash',
      type: 'danger',
      onClick,
      condition,
    })
  }

  view(onClick: (row: any) => void, condition?: (row: any) => boolean) {
    return this.add({
      text: '查看',
      icon: 'eye',
      type: 'info',
      onClick,
      condition,
    })
  }

  build() {
    return [...this.actions]
  }

  reset() {
    this.actions = []
    return this
  }
}
