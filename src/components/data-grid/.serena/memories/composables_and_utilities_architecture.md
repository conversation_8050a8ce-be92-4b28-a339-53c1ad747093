# Data-Grid Composables 和工具函数架构

## 🧩 核心 Composables 架构

### useDataGrid - 主要组合式函数
**位置**: `composables/useDataGrid.ts`
**设计模式**: 工厂函数 + 生命周期管理 + 资源清理

#### 函数签名
```typescript
export function useDataGrid(
  moduleModel: string,
  gridOptions: GridOptions
): UseDataGridReturn
```

#### 核心功能模块

##### 1. 初始化和配置管理
- **参数验证**: 开发环境运行时类型检查
- **插件管理器**: 自动获取/创建全局插件管理器
- **配置合并**: 深度合并默认配置和用户配置
- **Ref 隔离**: 确保每个实例有独立的 ref 状态

##### 2. 资源管理系统
**活跃实例跟踪**:
```typescript
const activeInstances = new Set<string>()
activeInstances.add(moduleModel)
```

**缓存机制**:
- `apiCache`: API 实例缓存，支持 LRU 策略
- `columnCache`: 列配置缓存
- `metadataCache`: 元数据缓存（5分钟过期）

**AbortController**: 取消进行中的 API 请求
```typescript
const abortController = ref<AbortController | null>(null)
```

##### 3. 定时器和防抖管理
**定时器集合**:
```typescript
const timers = ref<Set<ReturnType<typeof setTimeout>>>(new Set())
const createTimer = (callback: () => void, delay: number) => {
  const timer = setTimeout(() => {
    timers.value.delete(timer)
    callback()
  }, delay)
  timers.value.add(timer)
  return timer
}
```

**防抖函数工厂**:
```typescript
const createDebouncedFunction = <T extends (...args: any[]) => any>(
  key: string,
  fn: T,
  delay: number
): T => {
  // 支持取消的防抖实现
  // 自动管理清理
}
```

##### 4. 事件系统架构
**事件分层**:
- **VXE-Grid 原生事件**: 直接映射到 gridEvents
- **自定义事件**: 包装和增强的业务事件

**自定义事件列表**:
- `has-selection`: 是否有选择
- `selection-change`: 选择变化
- `selection-clear`: 清空选择
- `selection-count-change`: 选择数量变化

**事件状态管理**:
```typescript
let lastSelectionState = {
  count: 0,
  hasSelection: false,
  selection: [] as any[]
}

const emitSelectionEvents = () => {
  // 状态检查，避免重复触发
  // 智能事件合并
}
```

##### 5. 数据初始化流程
**异步初始化模式**:
```typescript
const initializeModelApi = async (): Promise<ModelApi | null>
const initializeColumns = async () => void
const initializeData = async () => void
```

**并行初始化**:
- 列配置和数据并行加载
- 缓存优先，降级到 API 调用
- 错误处理和取消支持

##### 6. 完善的清理机制
```typescript
const cleanup = () => {
  // 1. 取消所有进行中的API请求
  // 2. 清理所有定时器
  // 3. 取消所有防抖函数
  // 4. 清空grid引用
  // 5. 清空ModelApi引用
  // 6. 清空事件处理器
  // 7. 执行所有自定义清理任务
  // 8. 重置选择状态
  // 9. 智能清理缓存
}
```

#### 暴露的 API 接口
```typescript
interface UseDataGridReturn {
  // 核心状态
  moduleModel: string
  gridOptions: Ref<GridOptions>
  modelApi: Ref<ModelApi | null>
  loading: Ref<boolean>
  
  // 事件管理
  on: (events: Record<string, any>) => void
  
  // 数据操作
  handleDataFetch: (query: QueryParams) => Promise<void>
  refreshData: () => Promise<void>
  
  // 状态控制
  setLoading: (loading: boolean) => void
  
  // 缓存管理
  clearCache: () => void
  
  // 插件系统
  getPluginManager: () => PluginManager
  getColumnHelper: () => PluginHelper
  
  // 选择管理
  getSelection: () => any[]
  getSelectionCount: () => number
  hasSelection: () => boolean
  clearSelection: () => void
  setSelection: (rows: any[], checked?: boolean, type?: string) => void
  setAllSelection: (checked?: boolean) => void
  
  // 内部方法
  _setGridRef: (ref: VxeGridInstance) => void
  _emitCustomEvent: (eventName: string, payload: any) => void
  _hasCustomEventListeners: (eventNames: string[]) => boolean
}
```

## 🛠️ 工具函数架构

### utils/ 目录结构分析

#### columnHelpers.ts - 列配置工具
**核心功能**:
- `buildDataGridColumns()`: 构建数据网格列配置
- `enhanceColumn()`: 根据字段信息增强列配置
- `buildStatusMapFromMetadata()`: 从元数据构建状态映射

**智能列增强**:
```typescript
interface FieldInfo {
  name: string
  type: 'string' | 'integer' | 'float' | 'boolean' | 'datetime' | 'enum' | 'relation'
  comment?: string
  nullable?: boolean
  enum_info?: { enum_values: Record<string, string> }
  relation_info?: { direction: string }
}
```

**字段类型处理**:
- **数值字段**: 智能格式化（货币、百分比、小数）
- **枚举字段**: 自动映射枚举值
- **关系字段**: 多对一/一对多智能显示
- **日期字段**: 本地化日期格式
- **布尔字段**: 是/否显示

#### performanceMonitor.ts - 性能监控
**监控指标**:
```typescript
interface PerformanceMetrics {
  cacheHitRate: number
  averageResponseTime: number
  errorRate: number
  memoryUsage: number
}
```

**自动监控**:
- 缓存命中率统计
- API 响应时间监控
- 错误率统计
- 内存使用监控

#### ErrorHandler.ts - 错误处理
**错误分类**:
```typescript
interface ErrorConfig {
  message: string
  type: 'error' | 'warning' | 'info'
  category?: string
  context?: Record<string, any>
}
```

**预定义错误消息**:
- `GRID_INIT_FAILED`: 网格初始化失败
- `GRID_DATA_LOAD_FAILED`: 数据加载失败
- `GRID_REFRESH_FAILED`: 数据刷新失败
- `MODEL_API_NOT_LOADED`: 模型API未加载

#### typeGuards.ts - 类型守卫
**类型检查工具**:
```typescript
const TypeCheckers = {
  isString: (value: any): value is string
  isNumber: (value: any): value is number
  isBoolean: (value: any): value is boolean
  isArray: (value: any): value is any[]
  isObject: (value: any): value is object
  isFunction: (value: any): value is Function
}
```

**验证器**:
```typescript
const TypeValidators = {
  validateGridOptions: (options: any): ValidationResult
  validateColumnConfig: (config: any): ValidationResult
}
```

#### footerHelpers.ts - 页脚工具
**内置计算器**:
```typescript
const builtinCalculators = {
  sum: (values: number[]) => number
  avg: (values: number[]) => number
  min: (values: number[]) => number
  max: (values: number[]) => number
  count: (values: any[]) => number
}
```

**页脚方法工厂**:
```typescript
const createFooterMethod = (config: FooterConfig) => {
  // 动态创建 VxeTable 页脚方法
  // 支持多种计算类型
  // 智能数值提取和格式化
}
```

#### eventHelpers.ts - 事件工具
**选择管理工具**:
```typescript
const selectionHelpers = {
  getCheckboxRadioSelection: (gridRef: Ref<VxeGridInstance>) => any[]
  getCheckboxRadioSelectionCount: (gridRef: Ref<VxeGridInstance>) => number
  hasSelection: (gridRef: Ref<VxeGridInstance>) => boolean
  clearSelection: (gridRef: Ref<VxeGridInstance>) => void
  setSelection: (gridRef: Ref<VxeGridInstance>, rows: any[], checked: boolean, type?: string) => void
  setAllSelection: (gridRef: Ref<VxeGridInstance>, checked: boolean) => void
}
```

#### dataHelpers.ts - 数据工具
**数据获取封装**:
```typescript
const dataHelpers = {
  getDataGridData: async (
    gridOptionsRef: Ref<GridOptions>,
    modelApi: ModelApi,
    query?: QueryParams
  ) => void
}
```

**查询参数处理**:
- 分页参数转换
- 排序参数映射
- 过滤条件构建

#### mergeHelpers.ts - 合并工具
**深度合并**:
```typescript
const deepMerge = <T, U>(target: T, source: U): T & U
```

**特殊处理**:
- 数组合并策略
- Ref 对象保护
- 函数引用保持

## 🔄 架构模式和设计原则

### 1. 组合式设计
- **职责分离**: 每个工具模块专注特定功能
- **组合复用**: useDataGrid 组合多个工具模块
- **依赖注入**: 通过参数传递依赖关系

### 2. 资源管理
- **自动清理**: onUnmounted 自动清理资源
- **智能缓存**: LRU 缓存 + 智能失效
- **取消机制**: AbortController 取消请求

### 3. 错误处理
- **分层处理**: 工具层、组合式层、组件层
- **用户友好**: 错误消息本地化
- **开发友好**: 详细的错误上下文

### 4. 性能优化
- **防抖**: 高频操作防抖处理
- **缓存**: 多层缓存策略
- **懒加载**: 按需加载模块
- **监控**: 实时性能指标

### 5. 类型安全
- **TypeScript**: 完整的类型定义
- **运行时检查**: 开发环境类型验证
- **类型守卫**: 安全的类型转换

## 🎯 架构优势

### 可维护性
✅ **模块化设计**: 每个工具模块职责清晰
✅ **类型安全**: 完整的 TypeScript 支持
✅ **文档化**: 详细的接口文档

### 可扩展性
✅ **插件化**: 支持自定义工具扩展
✅ **配置驱动**: 灵活的配置系统
✅ **事件系统**: 解耦的事件通信

### 性能表现
✅ **智能缓存**: 多层缓存优化
✅ **资源管理**: 完善的清理机制
✅ **防抖节流**: 避免性能抖动

### 开发体验
✅ **组合式API**: 现代 Vue 3 开发模式
✅ **类型提示**: 完整的 IDE 支持
✅ **错误友好**: 清晰的错误信息

当前的 composables 和工具架构是一个**高度优化、生产就绪**的系统，提供了完整的数据网格开发基础设施。