# Data-Grid Plugins 系统当前架构

## 📁 实际目录结构

```
src/components/data-grid/plugins/
├── index.ts              # 主入口，导出核心渲染器和工厂函数
├── manager.ts            # PluginManager 和 PluginHelper 核心类
└── renderers/            # 实际渲染器组件目录
    ├── StatusRenderer.vue    # 状态渲染器（支持4种变体）
    ├── BooleanRenderer.vue   # 布尔值渲染器
    ├── LinkRenderer.vue      # 链接渲染器（支持邮箱、电话、URL）
    └── ActionsRenderer.vue   # 操作按钮渲染器
```

## 🏗️ 核心架构组件

### PluginManager 类
**位置**: `plugins/manager.ts`
**核心功能**:
- 组件注册管理：`registerComponent()`, `getComponent()`
- 渲染器注册：`registerRenderer()`, `getRenderer()`
- 列助手集成：`getColumnHelper()`
- 全局配置存储：`configStore` 暴露到 `window.__simpleConfigStore`

### PluginHelper 类
**核心功能**:
- **基础列创建**: `createColumn(field, title, plugin, config)`
- **专用列方法**: `status()`, `boolean()`, `link()`, `actions()`
- **专业链接类型**: `email()`, `phone()`, `url()`, `wechat()`, `qq()`, `skype()`
- **社交媒体**: `socialMedia()` 支持主流平台
- **渲染函数生成**: 自动生成 Vue 组件渲染标识

### 渲染器接口标准
**统一 Props 接口**:
```typescript
interface Props {
  value?: any                    // 单元格值
  row: any                      // 行数据
  config?: {                    // 渲染器配置
    [key: string]: any          // 特定配置项
  }
  field?: string                // 字段名
}
```

## 🎨 渲染器详细分析

### StatusRenderer.vue
**变体支持**: `badge` | `dot` | `text` | `progress`
**核心特性**:
- 4种视觉变体，支持图标和进度条
- 状态映射：`statusMap` 配置驱动
- 类型安全：完整的 TypeScript 支持
- 样式系统：TailwindCSS + 计算属性

**配置接口**:
```typescript
config?: {
  statusMap?: Record<string | number, StatusConfig>
  defaultStatus?: StatusConfig
  variant?: 'badge' | 'dot' | 'text' | 'progress'
  progress?: number
}

interface StatusConfig {
  text: string
  type?: string         // 颜色主题
  icon?: string         // 图标名
  progress?: number     // 进度值
}
```

### LinkRenderer.vue
**链接类型**: `url` | `mail` | `phone` | `auto`
**智能检测**:
- 自动检测链接类型（邮箱、电话、URL）
- 模板字符串支持：`${value}`, `{row.field}`
- URL 标准化处理
- 安全的外链处理

### BooleanRenderer.vue
**变体支持**: `switch` | `badge` | `icon` | `text`
**配置灵活性**:
- 自定义真/假文本
- 个性化真/假配置（文本、类型、图标）
- 多种视觉表现形式

### ActionsRenderer.vue
**布局模式**: `horizontal` | `dropdown`
**动作配置**:
```typescript
actions?: Array<{
  text: string
  icon?: string
  type?: 'primary' | 'success' | 'warning' | 'danger'
  onClick: (row: any) => void
  condition?: (row: any) => boolean
}>
```

## 🔧 核心配置机制

### 配置存储系统
- **configStore**: 全局 Map 存储，避免序列化问题
- **configId**: 唯一标识符，连接配置与组件实例
- **组件渲染标识**: `__COMPONENT__:${plugin}:${JSON.stringify(componentProps)}`

### 渲染器注册流程
1. **注册渲染器**: `manager.registerRenderer(renderer)`
2. **自动注册组件**: `manager.registerComponent(name, component)`
3. **列创建**: `helper.createColumn()` 生成列配置
4. **渲染函数**: 返回组件标识字符串，由 DataGrid 解析

## 🚀 Plugin 工厂函数

### createPluginManager()
**位置**: `plugins/index.ts`
**功能**: 创建并初始化插件管理器，注册所有核心渲染器

### coreRenderers 配置
```typescript
const coreRenderers: PluginRenderer[] = [
  {
    name: 'StatusRenderer',
    component: StatusRenderer,
    defaultConfig: { variant: 'badge' },
    defaultWidth: 120
  },
  // ... 其他渲染器
]
```

## 🔗 DataGrid 集成

### 列配置增强
**位置**: `utils/columnHelpers.ts`
**功能**: 
- `buildStatusMapFromMetadata()`: 从元数据构建状态映射
- 自动元数据处理和列增强
- 缓存机制：`metadataCache` 提升性能

### useDataGrid 集成
**插件管理器实例**:
```typescript
const pluginManager = getGlobalPluginManager()
const columnHelper = pluginManager.getColumnHelper()
```

**方法暴露**:
- `getPluginManager()`: 获取插件管理器实例
- `getColumnHelper()`: 获取列助手实例

## 📊 当前系统特点

### 优势
✅ **简洁直观**: 基于 Vue 组件的渲染器，易于理解
✅ **类型安全**: 完整的 TypeScript 支持
✅ **配置驱动**: 灵活的配置系统
✅ **组件复用**: 统一的接口标准
✅ **渐进增强**: 支持基础到高级的使用场景

### 架构特色
- **轻量级**: 相比之前的复杂企业级架构，当前实现更简洁
- **实用性**: 专注于实际业务需求的渲染场景
- **可扩展**: 易于添加新的渲染器类型
- **集成性**: 与 DataGrid 核心无缝集成

## 🔄 与之前企业级架构的差异

当前实现是一个**简化但实用的插件系统**，相比之前 memories 中记录的复杂企业级架构：

### 简化点
- 移除了复杂的依赖注入容器
- 简化了插件生命周期管理
- 减少了过度工程化的抽象层

### 保留的核心价值
- 组件化的渲染器架构
- 统一的配置接口
- 类型安全的开发体验
- 灵活的扩展机制

## 🎯 当前状态评估

**成熟度**: ✅ 生产就绪
**可维护性**: ✅ 结构清晰，易于维护
**扩展性**: ✅ 支持新渲染器添加
**性能**: ✅ 配置缓存，组件复用
**用户体验**: ✅ 功能完整，变体丰富

当前的插件系统已经是一个**实用、稳定、功能完整**的生产级实现。