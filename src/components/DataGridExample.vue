<!--
  DataGridExample.vue - 数据表格演示组件
  
  此组件演示了如何使用智能提示插件系统：
  1. 使用 getGlobalPluginManagerSync() 获取同步插件管理器
  2. 通过 getColumnHelper() 获得带智能提示的列配置方法
  3. 使用智能提示方法如 composite()、email()、phone()、boolean()、actions() 等
  
  核心特性：
  - ✅ 同步插件注册，无需等待
  - ✅ 完整的 TypeScript 智能提示支持
  - ✅ 简洁的列配置语法
  - ✅ 自动注册所有 renderers/ 插件
-->

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import DataGrid from '@/components/data-grid/core/DataGrid.vue'
import { useDataGrid } from '@/components/data-grid/composables/useDataGrid'

// 初始化插件管理器

// 创建 DataGrid 实例
const dataGridInstance = useDataGrid('demo/demo', {
  enableSelection: 'checkbox',
  columns: [], // 将在 onMounted 中使用智能提示设置
  height: 'auto',
  // 统计行配置
  footerConfig: {
    enabled: true,
    position: 'bottom',
    summaries: {
      customer_name: {
        label: '客户统计',
        style: { fontWeight: 'bold', color: '#1890ff' },
      },
      email: {
        calculator: 'countNonNull',
        label: '有邮箱',
        suffix: ' 个',
        style: { color: '#52c41a' },
      },
      phone: {
        calculator: 'countNonNull',
        label: '有电话',
        suffix: ' 个',
        style: { color: '#52c41a' },
      },
      total_amount: {
        calculator: 'sum',
        label: '总金额',
        prefix: '￥',
        style: { color: '#52c41a' },
      },
    },
  },
  toolbarOptions: {
    title: {
      main: '数据表格演示',
      sub: '智能提示插件系统',
      icon: 'mdi:table-large',
      badge: {
        text: 'DEMO',
      },
    },
    action: {
      create: {
        text: '新建',
        onClick: handleCreate,
      },
      bulkDelete: {
        text: '删除选中',
        condition: () => dataGridInstance.hasSelection(),
        onClick: handleBulkDelete,
      },
    },
  },
})

// 模板引用
const dataGridRef = ref()

// 事件处理函数
async function handleCreate() {
  try {
    const modelApi = dataGridInstance.modelApi.value
    if (modelApi?.create) {
      const newItem = {
        name: `新项目 ${Date.now()}`,
        description: '演示创建的新项目',
      }
      await modelApi.create(newItem)
      await dataGridInstance.refreshData()
      console.log('✅ 创建成功')
    } else {
      console.warn('ModelApi 不支持创建操作')
    }
  } catch (error) {
    console.error('❌ 创建失败:', error)
  }
}

async function handleBulkDelete() {
  try {
    const modelApi = dataGridInstance.modelApi.value
    const selection = dataGridInstance.getSelection()

    if (!modelApi?.bulkDelete || selection.length === 0) {
      console.warn('无法执行批量删除')
      return
    }

    const ids = selection.map((item: any) => item.id)
    await modelApi.bulkDelete(ids)
    dataGridInstance.clearSelection()
    await dataGridInstance.refreshData()
    console.log('✅ 删除成功')
  } catch (error) {
    console.error('❌ 删除失败:', error)
  }
}

// 操作按钮配置
const actionButtons = [
  {
    icon: 'mdi:pencil',
    text: '编辑',
    type: 'primary' as const,
    onClick: (row: any) => {
      console.log('编辑记录:', row.customer_name)
    },
  },
  {
    icon: 'mdi:delete',
    text: '删除',
    type: 'danger' as const,
    onClick: (row: any) => {
      console.log('删除记录:', row.customer_name)
    },
  },
  {
    icon: 'mdi:eye',
    text: '查看',
    type: 'info' as const,
    onClick: (row: any) => {
      console.log('查看记录:', row.customer_name)
    },
  },
]

// 初始化列配置
onMounted(() => {
  // 获取 ColumnHelper（智能提示立即可用）
  const columnHelper = dataGridInstance.getColumnHelper()

  // 使用简化的 API 创建列配置
  const columns = [
    // 客户信息列 - 基础显示
    // columnHelper.createColumn('customer_name', '客户信息', undefined, {
    //   width: 150,
    // }),
    columnHelper.composite('customer_info', '客户信息', {
      width: 150,
      main: {
        field: 'customer_name',
      },
      subs: {
        items: [
          { field: 'phone', label: '客户电话' },
          { field: 'email', label: '客户邮箱' },
        ],
        layout: 'horizontal',
        separator: ' · ',
      },
      actions: actionButtons,
      showActionsCount: 2,
      enableHover: true,
    }),

    // 邮箱列 - 专用邮箱Helper（演示阻止默认行为）
    columnHelper.email('email', '邮箱', {
      width: 200,
      className: 'text-green-800',
      onClick: (row: any) => {
        console.log('🔍 自定义邮箱处理:', row.email)
        // 演示：如果邮箱包含 'test'，则阻止默认行为
        if (row.email.includes('test')) {
          alert('测试邮箱，无法发送邮件')
          return false // 阻止默认的 mailto: 行为
        }
        if (!row.email.includes('@')) {
          alert('邮箱格式不正确，无法发送邮件')
          return false // 阻止默认的 mailto: 行为
        }
        console.log('✅ 继续执行默认邮箱行为')
        return true // 继续默认行为
      },
    }),

    // 电话列 - 专用电话Helper（演示继续默认行为）
    columnHelper.phone('phone', '电话', {
      width: 160,
      className: 'text-red-800',
      onClick: (row: any) => {
        console.log('📞 记录电话点击事件:', row.phone)
        // 不返回值，继续执行默认的 tel: 行为
      },
    }),
    columnHelper.status('status', '状态', {
      variant: 'badge',
      width: 120,
      autoFromMetadata: true,
    }),

    // VIP状态 - 布尔值渲染器
    columnHelper.boolean('is_vip', 'VIP客户', {
      trueText: 'VIP',
      falseText: '普通',
      variant: 'switch',
      width: 100,
    }),

    // 操作列 - 操作渲染器
    columnHelper.actions('操作', {
      width: 160,
      actions: actionButtons,
      fixed: 'right',
    }),
  ]
  // 设置列配置
  dataGridInstance.gridOptions.value.columns = columns as any

  // 注意：列配置会由 useDataGrid 自动处理
  // 这里只是演示如何创建列配置，实际使用时会通过插件系统自动注册
})
</script>

<template>
  <div class="space-y-4">
    <!-- 数据表格演示 - 展示智能提示插件系统 -->
    <div class="rounded-lg border">
      <DataGrid ref="dataGridRef" :data-grid-instance="dataGridInstance" />
    </div>
  </div>
</template>
